"use client";

import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Calendar } from "lucide-react";
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function NewsPage() {
  const newsItems = [
    {
      id: 1,
      title: "Vendor Hub Launches New Platform",
      date: "June 15, 2025",
      summary: "We're excited to announce the launch of our completely redesigned platform with enhanced features for both clients and vendors.",
      content: "Today marks a significant milestone for Vendor Hub as we unveil our completely redesigned platform. The new interface offers improved search capabilities, more detailed vendor profiles, and a streamlined communication system. Users can now filter vendors by specific service categories, geographical regions, and client testimonials."
    },
    {
      id: 2,
      title: "New Partnership with Industry Association",
      date: "May 28, 2025",
      summary: "Vendor Hub partners with the International HEOR Association to bring more specialized vendors to our platform.",
      content: "We're proud to announce our strategic partnership with the International HEOR Association. This collaboration will bring dozens of new specialized vendors to our platform and provide our users with access to exclusive industry resources and events. The partnership also includes joint educational webinars on best practices in vendor selection and management."
    },
    {
      id: 3,
      title: "Upcoming Webinar: Effective Vendor Selection",
      date: "May 10, 2025",
      summary: "Join our free webinar on strategies for selecting the right vendors for your HEOR and market access projects.",
      content: "Mark your calendars for our upcoming webinar on May 20th at 2:00 PM EST. Our panel of industry experts will discuss proven strategies for evaluating and selecting vendors for specialized HEOR and market access projects. Topics will include RFP best practices, evaluation criteria, and contract negotiation tips. Registration is free but space is limited."
    }
  ];

  return (
    <div className="container max-w-3xl mx-auto py-8 px-4">
      <Link href="/">
        <Button variant="ghost" className="mb-6 pl-0 hover:bg-transparent">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Vendor Hub
        </Button>
      </Link>
      
      <h1 className="text-3xl font-bold mb-6">Latest News</h1>
      
      <div className="space-y-6">
        {newsItems.map((item) => (
          <Card key={item.id} className="overflow-hidden">
            <CardHeader className="bg-amber-50 pb-4">
              <div className="flex items-center text-sm text-muted-foreground mb-1">
                <Calendar className="mr-2 h-4 w-4" />
                {item.date}
              </div>
              <CardTitle>{item.title}</CardTitle>
              <CardDescription className="text-base">{item.summary}</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <p>{item.content}</p>
              <Button variant="link" className="p-0 h-auto mt-2 text-amber-600 hover:text-amber-800">
                Read more
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <div className="mt-8 text-center">
        <Button variant="outline" className="border-amber-400 text-amber-700 hover:bg-amber-50">
          Load More News
        </Button>
      </div>
    </div>
  );
}