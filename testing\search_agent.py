import json
from typing import Annotated, List
from langchain.agents import create_react_agent  # Updated path
from langchain_core.tools import tool, InjectedToolCallId
from langgraph.prebuilt import InjectedState
from langgraph.graph import StateGraph, START, MessagesState
from langgraph.graph import END
from langgraph.types import Command
from langchain_core.messages import convert_to_messages
import os
from dotenv import load_dotenv
import openai

# Installation commands needed:
# pip install langchain
# pip install langchain-core
# pip install langgraph
# pip install python-dotenv
# pip install openai

load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

# Load vendor and publications data
VENDORS_PATH = os.path.join(
    os.path.dirname(__file__),
    "..",
    "heor_and_market_access_vendor_details.json",
)
PUBLICATIONS_PATH = os.path.join(
    os.path.dirname(__file__),
    "publications_scraping",
    "vendor_article_flags_combined.json",
)

with open(VENDORS_PATH, "r", encoding="utf-8") as f:
    VENDORS = json.load(f)

with open(PUBLICATIONS_PATH, "r", encoding="utf-8") as f:
    PUBLICATIONS = json.load(f)

# Simple search helpers for vendor and publication datasets

def search_vendors_by_name(name: str):
    results = [v for v in VENDORS if name.lower() in v.get('Company', '').lower()]
    return results[:5]

def search_vendors_by_service(service: str):
    results = []
    for v in VENDORS:
        services = str(v.get('Services', '')).lower()
        if service.lower() in services:
            results.append(v)
    return results[:5]

def _match_list(value, term):
    if isinstance(value, list):
        return any(term.lower() in str(v).lower() for v in value)
    return term.lower() in str(value).lower()

def search_publications_by_title(term: str):
    return [p for p in PUBLICATIONS if term.lower() in str(p.get("Title", "")).lower()][:5]

def search_publications_by_abstract(term: str):
    return [p for p in PUBLICATIONS if term.lower() in str(p.get("Abstract", "")).lower()][:5]

def search_publications_by_therapeutic_area(term: str):
    results = []
    for p in PUBLICATIONS:
        if _match_list(p.get("therapeutic_area", []), term):
            results.append(p)
    return results[:5]

def search_publications_by_region(term: str):
    results = []
    for p in PUBLICATIONS:
        if _match_list(p.get("region", []), term):
            results.append(p)
    return results[:5]

def search_publications_by_products(term: str):
    results = []
    for p in PUBLICATIONS:
        if _match_list(p.get("products", []), term):
            results.append(p)
    return results[:5]

def search_publications_by_study_type(term: str):
    results = []
    for p in PUBLICATIONS:
        if _match_list(p.get("study_type", []), term):
            results.append(p)
    return results[:5]

# Determine which publication fields are most relevant for a query
def classify_query_fields(query: str) -> List[str]:
    prompt = (
        "You are an assistant that selects the best publication fields to search based on a user query.\n"
        "Available fields: title, abstract, therapeuticArea, region, products, studyType.\n"
        "Return a JSON list of fields, for example ['title','abstract']. Use multiple fields when the query mentions more than one concept."
    )
    try:
        resp = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "system", "content": prompt}, {"role": "user", "content": query}],
            temperature=0,
        )
        content = resp.choices[0].message.content or "[]"
        fields = json.loads(content)
        if isinstance(fields, list) and all(isinstance(f, str) for f in fields):
            return fields
    except Exception as e:
        print("Field classification failed:", e)
    return ["title"]

def reflect_on_results(results: dict, used_fields: List[str], query: str) -> List[str]:
    if any(results.values()):
        return []
    prompt = (
        "The previous search returned no results. Suggest additional publication fields to try.\n"
        f"Already used fields: {', '.join(used_fields)}.\n"
        "Return a JSON list of new fields to search."
    )
    try:
        resp = openai.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "system", "content": prompt}, {"role": "user", "content": query}],
            temperature=0,
        )
        content = resp.choices[0].message.content or "[]"
        fields = json.loads(content)
        if isinstance(fields, list):
            return [f for f in fields if isinstance(f, str) and f not in used_fields]
    except Exception as e:
        print("Reflection failed:", e)
    return []

# Tools for agents

@tool
def name_search(query: str) -> str:
    """Search vendors by name."""
    matches = search_vendors_by_name(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def services_search(query: str) -> str:
    """Search vendors by services offered."""
    matches = search_vendors_by_service(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def title_search(query: str) -> str:
    """Search publications by title."""
    matches = search_publications_by_title(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def abstract_search(query: str) -> str:
    """Search publications by abstract."""
    matches = search_publications_by_abstract(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def therapeutic_area_search(query: str) -> str:
    """Search publications by therapeutic area."""
    matches = search_publications_by_therapeutic_area(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def region_search(query: str) -> str:
    """Search publications by region."""
    matches = search_publications_by_region(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def products_search(query: str) -> str:
    """Search publications by products."""
    matches = search_publications_by_products(query)
    return json.dumps(matches, ensure_ascii=False)

@tool
def study_type_search(query: str) -> str:
    """Search publications by study type."""
    matches = search_publications_by_study_type(query)
    return json.dumps(matches, ensure_ascii=False)

# Create sub-agents
name_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[name_search],
    prompt=(
        "You are a vendor name search agent.\n"
        "Use the name_search tool to look up vendors by name and return results."),
    name="name_agent",
)

services_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[services_search],
    prompt=(
        "You are a vendor services search agent.\n"
        "Use the services_search tool to find vendors offering particular services."),
    name="services_agent",
)

title_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[title_search],
    prompt=(
        "You are a publications title search agent.\n"
        "Use the title_search tool to find articles by title."),
    name="title_agent",
)

abstract_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[abstract_search],
    prompt=(
        "You are a publications abstract search agent.\n"
        "Use the abstract_search tool to find articles by abstract."),
    name="abstract_agent",
)

therapeutic_area_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[therapeutic_area_search],
    prompt=(
        "You are a therapeutic area search agent.\n"
        "Use the therapeutic_area_search tool to match therapeutic areas."),
    name="therapeutic_area_agent",
)

region_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[region_search],
    prompt=(
        "You are a region search agent.\n"
        "Use the region_search tool to find articles by geographic region."),
    name="region_agent",
)

products_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[products_search],
    prompt=(
        "You are a products search agent.\n"
        "Use the products_search tool to find articles mentioning products."),
    name="products_agent",
)

study_type_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[study_type_search],
    prompt=(
        "You are a study type search agent.\n"
        "Use the study_type_search tool to find articles by study design."),
    name="study_type_agent",
)

# Helper to create handoff tools

def create_handoff_tool(agent_name: str, description: str):
    name = f"handoff_to_{agent_name}"

    @tool(name, description=description)
    def handoff(
        state: Annotated[MessagesState, InjectedState],
        tool_call_id: Annotated[str, InjectedToolCallId],
    ) -> Command:
        msg = {
            "role": "tool",
            "content": f"Transferred to {agent_name}",
            "name": name,
            "tool_call_id": tool_call_id,
        }
        return Command(goto=agent_name, update={**state, "messages": state["messages"] + [msg]}, graph=Command.PARENT)

    return handoff

handoff_name = create_handoff_tool("name_agent", "Delegate vendor name search")
handoff_services = create_handoff_tool("services_agent", "Delegate services search")
handoff_title = create_handoff_tool("title_agent", "Delegate title search")
handoff_abstract = create_handoff_tool("abstract_agent", "Delegate abstract search")
handoff_therapeutic_area = create_handoff_tool("therapeutic_area_agent", "Delegate therapeutic area search")
handoff_region = create_handoff_tool("region_agent", "Delegate region search")
handoff_products = create_handoff_tool("products_agent", "Delegate products search")
handoff_study_type = create_handoff_tool("study_type_agent", "Delegate study type search")

# Supervisor agent
supervisor_agent = create_react_agent(
    model="openai:gpt-4o-mini",
    tools=[
        handoff_name,
        handoff_services,
        handoff_title,
        handoff_abstract,
        handoff_therapeutic_area,
        handoff_region,
        handoff_products,
        handoff_study_type,
    ],
    prompt=(
        "You are a supervisor managing search agents.\n"
        "Choose the best agent based on the user's request.\n"
        "Available agents handle title, abstract, therapeutic area, region, products, and study type searches.\n"
        "Delegate using the provided tools and return only the agent's result."),
    name="supervisor",
)

# Build the graph
supervisor = (
    StateGraph(MessagesState)
    .add_node(
        supervisor_agent,
        destinations=(
            "name_agent",
            "services_agent",
            "title_agent",
            "abstract_agent",
            "therapeutic_area_agent",
            "region_agent",
            "products_agent",
            "study_type_agent",
            END,
        ),
    )
    .add_node(name_agent)
    .add_node(services_agent)
    .add_node(title_agent)
    .add_node(abstract_agent)
    .add_node(therapeutic_area_agent)
    .add_node(region_agent)
    .add_node(products_agent)
    .add_node(study_type_agent)
    .add_edge(START, "supervisor")
    .add_edge("name_agent", "supervisor")
    .add_edge("services_agent", "supervisor")
    .add_edge("title_agent", "supervisor")
    .add_edge("abstract_agent", "supervisor")
    .add_edge("therapeutic_area_agent", "supervisor")
    .add_edge("region_agent", "supervisor")
    .add_edge("products_agent", "supervisor")
    .add_edge("study_type_agent", "supervisor")
    .compile()
)

if __name__ == "__main__":
    print("Running multi-agent search. Type 'exit' to quit.")
    while True:
        query = input("Query> ")
        if query.lower() == "exit":
            break

        fields = classify_query_fields(query)
        used_fields = list(fields)
        results: dict[str, str] = {f: "" for f in fields}

        for field in fields:
            user_msg = f"Search {field} for '{query}'"
            for chunk in supervisor.stream({"messages": [{"role": "user", "content": user_msg}]}):
                msgs = convert_to_messages(chunk["supervisor"]["messages"])
                result = msgs[-1].content
            results[field] = result

        extra_fields = reflect_on_results(results, used_fields, query)
        for field in extra_fields:
            used_fields.append(field)
            user_msg = f"Search {field} for '{query}'"
            for chunk in supervisor.stream({"messages": [{"role": "user", "content": user_msg}]}):
                msgs = convert_to_messages(chunk["supervisor"]["messages"])
                result = msgs[-1].content
            results[field] = result

        print(json.dumps(results, ensure_ascii=False, indent=2))