"use client";

import React, { useState, useEffect, useRef, use<PERSON>allback, useMemo } from 'react';
import Vendor<PERSON><PERSON><PERSON>ogo from '@/icons/vendorhub_logo.svg';
import EvicenterLogo from '@/icons/Evicenter.svg?url';
import EvicenterWhiteLogo from '@/icons/evicenter-white.svg?url';
import { getVend<PERSON>, Vendor } from '@/services/vendor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from "@/hooks/use-toast";
import { useToast } from "@/components/ui/use-toast";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Toaster } from "@/components/ui/toaster";
import { Input } from "@/components/ui/input";
import { PanelRight, X, Globe, Linkedin, Copy, ListChecks, Check, ClipboardList, MessageSquare, Search, Plus, FileText, ChevronDown, Book, Database } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import Image from 'next/image';
import { cn } from "@/lib/utils";
import OpenAILogo from '@/icons/openai-icon.svg?url';
import OpenAILogoWhite from '@/icons/openai-icon-white.svg?url';
import ClaudeAILogo from '@/icons/claude-ai-icon.svg?url';
import GoogleGeminiLogo from '@/icons/google-gemini-icon.svg?url';
import PerplexityLogo from '@/icons/perplexity.svg?url';
import PerplexityLogoWhite from '@/icons/perplexity-white.svg?url';
import PubMedLogo from '@/icons/pubmed_logo.svg?url';
import PubMedLogoDark from '@/icons/pubmed_logo_darkmode.svg?url';
import { MessageTemplateForm } from "@/components/MessageTemplateForm";
import { TutorialWidget } from "@/components/TutorialWidget";
import { Chatbot } from "@/components/Chatbot";
import { ThemeToggle } from '@/components/ThemeToggle';
import { useTheme } from '@/contexts/ThemeContext';
import { getCountryCode } from '../utils/countryUtils';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-mobile';

interface VendorWithCountry extends Omit<Vendor, 'services'> {
  country: string;
  featured: boolean;
  services: string;
  dataProvider: boolean; // Add this property
}

interface Position {
  x: number;
  y: number;
}

const MIN_PANEL_WIDTH = 400; // minimum width in pixels
const MAX_PANEL_WIDTH = 800; // maximum width in pixels
const DEFAULT_PANEL_WIDTH = 400; // default starting width in pixels

const VendorCard = React.memo(({ 
  vendor, 
  selectedVendorIds, 
  toggleVendorSelection, 
  servicesSearchQuery,
  openServiceVendors,
  toggleVendorServices,
  synonymMap,
  globalPubSearchQuery,
  globalPubSearchType,
  globalPubSearchFields
}: {
  vendor: VendorWithCountry,
  selectedVendorIds: string[],
  toggleVendorSelection: (vendorId: string) => void,
  servicesSearchQuery: string,
  openServiceVendors: Set<string>,
  toggleVendorServices: (vendorId: string, isOpen: boolean) => void,
  synonymMap: Record<string, string[]>,
  globalPubSearchQuery: string,
  globalPubSearchType: string,
  globalPubSearchFields: Record<string, string>
}) => {
  const [logoError, setLogoError] = useState(false);
  const [showPublications, setShowPublications] = useState(false);
  const [publications, setPublications] = useState<any[]>([]);
  const [isLoadingPublications, setIsLoadingPublications] = useState(false);
  const isSelected = selectedVendorIds.includes(vendor.id);
  const showServices = openServiceVendors.has(vendor.id);
  const { theme } = useTheme();
  
  // Function to fetch publications
  const fetchPublications = async () => {
    if (showPublications && publications.length > 0) {
      // If already showing publications, just toggle off
      setShowPublications(false);
      return;
    }
    
    try {
      setIsLoadingPublications(true);
      setShowPublications(true);
      
      // Build the API URL with appropriate parameters
      let apiUrl = `/api/vendor-publications?name=${encodeURIComponent(vendor.name)}`;
      
      // Add global search parameters if they exist
      if (globalPubSearchType && globalPubSearchQuery) {
        apiUrl += `&${globalPubSearchType}=${encodeURIComponent(globalPubSearchQuery)}`;
      }
      
      // Add therapeutic area search if it exists
      if (servicesSearchQuery) {
        apiUrl += `&therapeuticArea=${encodeURIComponent(servicesSearchQuery)}`;
      }
      
      // Add all active search fields from the global publication search
      if (globalPubSearchFields) {
        Object.entries(globalPubSearchFields).forEach(([field, value]) => {
          if (value && value.trim() !== '') {
            apiUrl += `&${field}=${encodeURIComponent(value)}`;
          }
        });
      }
      
      const response = await fetch(apiUrl);
      
      if (!response.ok) {
        throw new Error('Failed to fetch publications');
      }
      
      const data = await response.json();
      setPublications(data.publications || []);
    } catch (error) {
      console.error('Error fetching publications:', error);
      toast({
        title: "Error",
        description: "Failed to load publications",
        variant: "destructive",
      });
      setShowPublications(false);
    } finally {
      setIsLoadingPublications(false);
    }
  };

  // Use the logoUrl directly from the vendor object
  const logoSrc = vendor.logoUrl;
  
  // Get country code for flag
  const countryCode = getCountryCode(vendor.hqCountry);
  const flagUrl = countryCode ? `/flags/${countryCode.toLowerCase()}.svg` : null;

  // Update the services toggle to use the shared state
  const handleToggleServices = () => {
    toggleVendorServices(vendor.id, !showServices);
  };

  // Enhanced highlightText function that also highlights synonyms
  const highlightText = (text: string, searchQuery: string) => {
    if (!searchQuery) return text;
    const terms = searchQuery.toLowerCase().split(' ').filter(t => t);
    let result = text;
    
    terms.forEach(term => {
      // Get all synonyms for this term
      const allTermsToHighlight = new Set([term]);
      
      // Add synonyms to the set of terms to highlight
      const synonyms = synonymMap[term] || [];
      synonyms.forEach(synonym => allTermsToHighlight.add(synonym));
      
      // Highlight each term and its synonyms
      allTermsToHighlight.forEach(termToHighlight => {
        if (termToHighlight.length > 2) { // Only highlight terms longer than 2 chars to avoid highlighting common words
          const re = new RegExp(`(${termToHighlight})`, 'gi');
          result = result.replace(re, '<mark style="background-color: ' + 
            (theme === 'dark' 
              ? 'rgb(251, 191, 36, 0.7)' 
              : 'rgb(251, 191, 36, 0.4)') + 
            '; padding: 3px;">$1</mark>');
        }
      });
    });
    
    return result;
  };

  const formatServices = (services: string, searchQuery: string) => {
    if (!services) return "No services listed.";
    return services
      .split('\n')
      .map(s => s.replace(/^[-•\s]+/, '').trim())
      .filter(s => s && s !== '-')
      .map(s => `• ${highlightText(s, searchQuery)}`)
      .join('\n');
  };

  return (
    <Card 
      key={vendor.id}
      className={cn(
        "transition-colors duration-200 h-full relative overflow-hidden",
        isSelected 
          ? "shadow-md border-amber-300 dark:border-amber-700 " + 
            (theme === 'dark' 
              ? "dark:bg-amber-950/30" 
              : "bg-amber-50/50")
          : ""
      )}
    >
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Avatar className="w-8 h-8 flex-shrink-0">
            {!logoError && logoSrc && !logoSrc.includes('picsum') ? (
              <div className="relative w-full h-full">
                <Image
                  src={logoSrc}
                  alt={`${vendor.name} logo`}
                  fill
                  className="object-contain p-1"
                  onError={(e) => {
                    console.error('Image load error for', vendor.name, e);
                    setLogoError(true);
                  }}
                  sizes="32px"
                />
              </div>
            ) : (
              <AvatarFallback className={isSelected ? "bg-amber-100 text-amber-800" : ""}>
                {vendor.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            )}
          </Avatar>
          
          {/* Agency name in its own container that can truncate */}
          <div className="flex-1 min-w-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="truncate font-medium block">{vendor.name}</span>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{vendor.name}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          {/* Right-aligned badges container */}
          <div className="flex items-center gap-2 flex-shrink-0 ml-auto">
            {vendor.dataProvider && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex-shrink-0" title="Data Provider">
                      <Database className="h-4 w-4 text-blue-500" />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Data Provider</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
            {flagUrl && (
              <div 
                className="flex-shrink-0 w-5 h-5 relative rounded-full overflow-hidden border border-gray-200 dark:border-gray-700" 
                title={vendor.hqCountry}
              >
                <Image
                  src={flagUrl}
                  alt={`${vendor.hqCountry} flag`}
                  fill
                  className="object-cover"
                  onError={(e) => {
                    console.error('Flag load error for', vendor.hqCountry, e);
                  }}
                  sizes="24px"
                />
              </div>
            )}
            {vendor.featured && (
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-[10px] h-5 flex-shrink-0">
                Featured
              </Badge>
            )}
          </div>
        </CardTitle>
        <CardDescription className="truncate">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="truncate">{vendor.description}</span>
              </TooltipTrigger>
              <TooltipContent>
                <p>{vendor.description}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col gap-3">
          {/* Icon row - organized in a consistent grid */}
          <div className="grid grid-cols-5 gap-1 items-center">
            {/* Website icon */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <a 
                    href={vendor.websiteUrl} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="flex items-center justify-center h-8 w-8 rounded-md hover:bg-muted/50 transition-colors"
                  >
                    <Globe className="h-4 w-4" />
                  </a>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Website</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* LinkedIn icon */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <a 
                    href={vendor.linkedinUrl} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="flex items-center justify-center h-8 w-8 rounded-md hover:bg-muted/50 transition-colors"
                  >
                    <Linkedin className="h-4 w-4" />
                  </a>
                </TooltipTrigger>
                <TooltipContent>
                  <p>LinkedIn</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* Publications link icon - show greyed out if not available */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  {vendor.publications && vendor.publications !== "" ? (
                    <a 
                      href={vendor.publications} 
                      target="_blank" 
                      rel="noopener noreferrer" 
                      className="flex items-center justify-center h-8 w-8 rounded-md hover:bg-muted/50 transition-colors"
                    >
                      <FileText className="h-4 w-4" />
                    </a>
                  ) : (
                    <div className="flex items-center justify-center h-8 w-8">
                      <FileText className="h-4 w-4 text-gray-300 dark:text-gray-600" />
                    </div>
                  )}
                </TooltipTrigger>
                <TooltipContent>
                  {vendor.publications && vendor.publications !== "" ? (
                    <p>Publications</p>
                  ) : (
                    <p>No publications page</p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* PubMed Publications Button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    onClick={fetchPublications} 
                    className="flex items-center justify-center h-8 w-8 p-0 rounded-md hover:bg-muted/50 transition-colors"
                  >
                    <div className="relative w-6 h-6">
                      <Image
                        src={theme === 'dark' ? PubMedLogoDark : PubMedLogo}
                        alt="PubMed Publications"
                        fill
                        className="object-contain"
                      />
                    </div>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>View related publications</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            {/* Services button */}
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    onClick={handleToggleServices} 
                    className={cn(
                      "flex items-center justify-center h-8 w-8 p-0 rounded-md transition-colors",
                      showServices 
                        ? (theme === 'dark' ? "bg-black text-amber-300" : "bg-black text-amber-300")
                        : (theme === 'dark' ? "hover:bg-muted/50" : "hover:bg-transparent text-black hover:text-black")
                    )}
                  >
                    <ListChecks className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {showServices ? "Hide services" : "Show services"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          {/* Selection checkbox with email status indicator inline */}
          <div className="mt-2">
            <div 
              className={cn(
                "flex items-center justify-between cursor-pointer rounded-md p-1 transition-colors",
                isSelected ? "bg-amber-100/50" : "hover:bg-muted/50"
              )}
              onClick={() => toggleVendorSelection(vendor.id)}
            >
              <div className="flex items-center">
                <Checkbox
                  checked={isSelected}
                  onCheckedChange={() => {}}
                  className={cn(
                    "mt-0",
                    isSelected ? "border-amber-500 data-[state=checked]:bg-amber-500" : ""
                  )}
                />
                <div className="ml-2 h-6 flex items-center">
                  <span className={cn(
                    "text-sm truncate",
                    isSelected ? "font-medium text-black" : "text-muted-foreground"
                  )}>
                    {isSelected ? "Selected" : "Select agency"}
                  </span>
                </div>
              </div>
              
              {/* Email status indicator - inline with checkbox */}
              {!vendor.email && (
                <span className="text-xs text-gray-500 italic dark:text-gray-400 ml-auto">
                  No email address
                </span>
              )}
            </div>
          </div>
          
          {/* Services section - unchanged */}
          {showServices && (
            <div className="space-y-2 relative mt-2">
              <div 
                className="text-sm bg-muted/50 rounded-md p-2 overflow-auto max-h-40 relative"
                ref={(el) => {
                  if (el) {
                    // Check if content overflows
                    const hasOverflow = el.scrollHeight > el.clientHeight;
                    // Add or remove data attribute based on overflow
                    if (hasOverflow) {
                      el.setAttribute('data-has-overflow', 'true');
                    } else {
                      el.removeAttribute('data-has-overflow');
                    }
                  }
                }}
              >
                <ul style={{ 
                  listStyleType: 'none', 
                  margin: 0, 
                  padding: 0 
                }}>
                  {formatServices(vendor.services, servicesSearchQuery).split('\n').map((service, index) => (
                    <li 
                      key={index} 
                      style={{
                        paddingLeft: '0.75em',
                        textIndent: '-0.65em',
                        marginBottom: '0.5em',
                        overflowWrap: 'break-word',
                        wordWrap: 'break-word',
                        wordBreak: 'break-word'
                      }}
                      dangerouslySetInnerHTML={{ __html: service }}
                    />
                  ))}
                </ul>
                
                {/* Scroll indicator - simple version */}
                <div 
                  className="absolute bottom-0 left-0 right-0 h-6 pointer-events-none"
                  style={{
                    background: 'linear-gradient(to top, var(--background) 0%, transparent 100%)',
                    opacity: 0.8,
                    display: 'var(--show-indicator, none)'
                  }}
                >
                </div>
              </div>
            </div>
          )}
          
          {/* Add CSS for the scroll indicator */}
          <style jsx global>{`
            /* Show indicator only when content overflows */
            [data-has-overflow="true"] {
              --show-indicator: block;
            }
            
            /* Hide indicator when scrolled to bottom */
            [data-has-overflow="true"]::-webkit-scrollbar {
              width: 6px;
            }
            
            [data-has-overflow="true"]::-webkit-scrollbar-track {
              background: transparent;
            }
            
            [data-has-overflow="true"]::-webkit-scrollbar-thumb {
              background-color: rgba(155, 155, 155, 0.5);
              border-radius: 3px;
            }
          `}</style>
          
          {/* Publications section - unchanged */}
          {showPublications && (
            <div className="mt-4 border-t pt-4">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-bold">Publications</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  onClick={() => setShowPublications(false)}
                >
                  <X className="h-4 w-4" />
                  <span className="sr-only">Close</span>
                </Button>
              </div>
              
              {isLoadingPublications ? (
                <div className="flex justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-amber-500"></div>
                </div>
              ) : publications.length > 0 ? (
                <ul className="space-y-2 text-sm max-h-60 overflow-y-auto">
                  {publications.map((pub, index) => (
                    <li key={index} className="border-b border-gray-100 dark:border-gray-800 pb-2 mb-2">
                      <p className="font-medium">{pub.Title}</p>
                      <p className="text-xs text-muted-foreground truncate">{pub.Abstract?.substring(0, 100)}...</p>
                      
                      {pub.therapeutic_area && pub.therapeutic_area.length > 0 && (
                        <div className="mt-1">
                          <span className="text-xs font-medium">Therapeutic areas: </span>
                          <span className="text-xs text-muted-foreground">
                            {pub.therapeutic_area.join(', ')}
                          </span>
                        </div>
                      )}
                      
                      {pub.PMID && (
                        <a 
                          href={`https://pubmed.ncbi.nlm.nih.gov/${pub.PMID}/`} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300 mt-1 inline-block"
                        >
                          View on PubMed (PMID: {pub.PMID})
                        </a>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-muted-foreground py-2">No publications found for this vendor.</p>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

VendorCard.displayName = "VendorCard";

export default function Home() {
  const isMobile = useIsMobile();
  
  const [vendors, setVendors] = useState<VendorWithCountry[]>([]);
  const [selectedVendorIds, setSelectedVendorIds] = useState<string[]>([]);
  const [message, setMessage] = useState('');
  const [subject, setSubject] = useState('');
  const { toast } = useToast();
  const [filteredVendors, setFilteredVendors] = useState<VendorWithCountry[]>([]);
  const [isPanelOpen, setIsPanelOpen] = useState(true);
  const [emailList, setEmailList] = useState('');
  const emailListRef = useRef<HTMLTextAreaElement>(null);
  const subjectRef = useRef<HTMLTextAreaElement>(null);
  const messageRef = useRef<HTMLTextAreaElement>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const userEmail = '<EMAIL>';
  const { theme } = useTheme();
  const [servicesSearchQuery, setServicesSearchQuery] = useState('');
  const [panelWidth, setPanelWidth] = useState(DEFAULT_PANEL_WIDTH);
  const [isDragging, setIsDragging] = useState(false);
  const [startPosition, setStartPosition] = useState<Position>({ x: 0, y: 0 });
  const resizeHandleRef = useRef<HTMLDivElement>(null);
  const [copiedEmail, setCopiedEmail] = useState(false);
  const [copiedSubject, setCopiedSubject] = useState(false);
  const [copiedMessage, setCopiedMessage] = useState(false);
  const [adImages, setAdImages] = useState<string[]>([]);
  const [shuffledAds, setShuffledAds] = useState<string[]>([]);
  const [isTemplateFormOpen, setIsTemplateFormOpen] = useState(false);
  const [synonymMap, setSynonymMap] = useState<Record<string, string[]>>({});
  const [searchType, setSearchType] = useState<'name' | 'services' | 'dataProvider'>('name');
  const [baseFilteredVendors, setBaseFilteredVendors] = useState<VendorWithCountry[]>([]);
  
  // Add these new state variables for publication search
  const [pubSearchTitle, setPubSearchTitle] = useState('');
  const [pubSearchAbstract, setPubSearchAbstract] = useState('');
  const [pubSearchTherapeuticArea, setPubSearchTherapeuticArea] = useState('');
  const [pubSearchRegion, setPubSearchRegion] = useState('');
  const [pubSearchProducts, setPubSearchProducts] = useState('');
  const [pubSearchStudyType, setPubSearchStudyType] = useState('');
  const [pubSearchType, setPubSearchType] = useState<string>('title');
  const [pubSearchQuery, setPubSearchQuery] = useState('');
  const [pubSearchLoading, setPubSearchLoading] = useState(false);
  const [pubSearchVendors, setPubSearchVendors] = useState<string[]>([]);

  // Add these new state variables for multi-field publication search
  const [pubSearchFields, setPubSearchFields] = useState<Record<string, string>>({
    title: '',
    abstract: '',
    therapeuticArea: '',
    region: '',
    products: '',
    studyType: ''
  });
  const [activeSearchFields, setActiveSearchFields] = useState<string[]>([]);

  // Add these state variables for the left panel
  const [leftPanelWidth, setLeftPanelWidth] = useState(250); // Default width of 96 (w-96)
  const [isLeftDragging, setIsLeftDragging] = useState(false);
  const [leftStartPosition, setLeftStartPosition] = useState<Position>({ x: 0, y: 0 });
  const leftResizeHandleRef = useRef<HTMLDivElement>(null);

  // Add constants for left panel
  const MIN_LEFT_PANEL_WIDTH = 250;
  const MAX_LEFT_PANEL_WIDTH = 600;

  // Add these handlers for the left panel resizing
  const handleLeftMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent text selection during drag
    setIsLeftDragging(true);
    setLeftStartPosition({
      x: e.pageX,
      y: e.pageY
    });
  };

  const handleLeftMouseMove = useCallback((e: MouseEvent) => {
    if (isLeftDragging) {
      const deltaX = e.pageX - leftStartPosition.x;
      const newWidth = Math.max(
        MIN_LEFT_PANEL_WIDTH,
        Math.min(MAX_LEFT_PANEL_WIDTH, leftPanelWidth + deltaX)
      );
      setLeftPanelWidth(newWidth);
      setLeftStartPosition({
        x: e.pageX,
        y: e.pageY
      });
    }
  }, [isLeftDragging, leftStartPosition, leftPanelWidth]);

  const handleLeftMouseUp = useCallback(() => {
    setIsLeftDragging(false);
  }, []);

  // Add this useEffect for left panel mouse events
  useEffect(() => {
    if (isLeftDragging) {
      window.addEventListener('mousemove', handleLeftMouseMove);
      window.addEventListener('mouseup', handleLeftMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleLeftMouseMove);
      window.removeEventListener('mouseup', handleLeftMouseUp);
    };
  }, [isLeftDragging, handleLeftMouseMove, handleLeftMouseUp]);

  // Function to shuffle array (Fisher-Yates algorithm)
  const shuffleArray = (array: string[]) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent text selection during drag
    setIsDragging(true);
    setStartPosition({
      x: e.pageX,
      y: e.pageY
    });
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging) {
      const deltaX = startPosition.x - e.pageX;
      const newWidth = Math.max(
        MIN_PANEL_WIDTH,
        Math.min(MAX_PANEL_WIDTH, panelWidth + deltaX)
      );
      setPanelWidth(newWidth);
      setStartPosition({
        x: e.pageX,
        y: e.pageY
      });
    }
  }, [isDragging, startPosition, panelWidth]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  useEffect(() => {
    const loadVendors = async () => {
      const vendorList: Vendor[] = await getVendors();
      const vendorsWithCountry: VendorWithCountry[] = vendorList.map((vendor) => ({
        ...vendor,
        country: ['USA', 'Canada'][Math.floor(Math.random() * 2)], // or get from HQ country in JSON
        featured: vendor.featured, // Use the featured status from the vendor data
        dataProvider: vendor.dataProvider // Add the dataProvider property
      }));
      
      // Sort vendors to put featured ones at the top, then shuffle the non-featured ones
      const featuredVendors = vendorsWithCountry.filter(v => v.featured);
      const nonFeaturedVendors = vendorsWithCountry.filter(v => !v.featured);
      
      // Shuffle only the non-featured vendors using Fisher-Yates algorithm
      for (let i = nonFeaturedVendors.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [nonFeaturedVendors[i], nonFeaturedVendors[j]] = [nonFeaturedVendors[j], nonFeaturedVendors[i]];
      }
      
      // Combine featured vendors (at the top) with shuffled non-featured vendors
      const sortedVendors = [...featuredVendors, ...nonFeaturedVendors];
      
      setVendors(sortedVendors);
      setFilteredVendors(sortedVendors);
    };

    loadVendors();
  }, []);

  useEffect(() => {
    // Only set the initial email list if it's empty
    if (!emailList) {
      const selectedEmails = vendors
        .filter((vendor) => selectedVendorIds.includes(vendor.id))
        .map((vendor) => vendor.email)
        .join(', ');
      setEmailList(selectedEmails);
    }
  }, [selectedVendorIds, vendors]); // Remove emailList from dependencies

  const handleEmailListChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEmailList(e.target.value);
    if (emailListRef.current) {
      adjustTextareaHeight(emailListRef.current);
    }
  };

  const adjustTextareaHeight = (textarea: HTMLTextAreaElement) => {
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight}px`;
  };

  const toggleVendorSelection = useCallback((vendorId: string) => {
    setSelectedVendorIds((prevSelected) => {
      const isCurrentlySelected = prevSelected.includes(vendorId);
      
      // Get current email list as array
      const currentEmails = emailList.split(/[,;]\s*/).filter(email => email.trim());
      
      if (isCurrentlySelected) {
        // Remove the vendor's email if it exists in any form in the current list
        const vendorToRemove = vendors.find(vendor => vendor.id === vendorId);
        if (vendorToRemove) {
          const updatedEmails = currentEmails.filter(email => 
            email.toLowerCase() !== vendorToRemove.email.toLowerCase()
          );
          setEmailList(updatedEmails.join(', '));
          // Adjust height after state update
          setTimeout(() => {
            if (emailListRef.current) {
              adjustTextareaHeight(emailListRef.current);
            }
          }, 0);
        }
      } else {
        // Add the vendor's email only if it's not already in the list
        const vendorToAdd = vendors.find(vendor => vendor.id === vendorId);
        if (vendorToAdd && !currentEmails.some(email => 
          email.toLowerCase() === vendorToAdd.email.toLowerCase()
        )) {
          const updatedEmails = [...currentEmails, vendorToAdd.email];
          setEmailList(updatedEmails.join(', '));
          // Adjust height after state update
          setTimeout(() => {
            if (emailListRef.current) {
              adjustTextareaHeight(emailListRef.current);
            }
          }, 0);
        }
      }

      return isCurrentlySelected
        ? prevSelected.filter((id) => id !== vendorId)
        : [...prevSelected, vendorId];
    });
  }, [vendors, emailList]); // Keep emailList in dependencies here

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSubject(e.target.value);
  };

  const handleOpenNativeOutlook = () => {
    // Use the emailList state directly instead of regenerating from vendors
    if (!emailList.trim()) {
      toast({
        title: "No recipients selected",
        description: "Please add at least one email address",
        variant: "destructive",
      });
      return;
    }

    // Format the email list for the mailto link
    // Split by commas or semicolons, trim whitespace, and rejoin with commas
    const formattedEmails = emailList
      .split(/[,;]\s*/)
      .filter(email => email.trim())
      .join(',');

    const mailtoLink = `mailto:?bcc=${encodeURIComponent(formattedEmails)}&subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(message)}`;
    window.location.href = mailtoLink;
  };

  const handleOpenOutlookWebApp = () => {
    try {
      // Use the emailList state directly instead of regenerating from vendors
      if (!emailList.trim()) {
        toast({
          title: "No recipients selected",
          description: "Please select at least one vendor",
          variant: "destructive",
        });
        return;
      }

      // Format message with BCC recipients at the top
      const instructionText = 'Please copy these emails into the BCC field:';
      const formattedMessage = `${instructionText}\n${emailList}\n\n${message}`;

      // Format the content for clipboard using the user-edited emailList
      const contentToCopy = `${emailList}\n\n${subject}\n\n${message}`;

      // Copy to clipboard using the modern clipboard API
      navigator.clipboard.writeText(contentToCopy)
        .then(() => {
          const outlookWebUrl = 'https://outlook.office.com/mail/deeplink/compose';
          
          const encodedSubject = encodeURIComponent(subject);
          const encodedBody = encodeURIComponent(formattedMessage).replace(/\+/g, '%20');
          
          const fullUrl = `${outlookWebUrl}?subject=${encodedSubject}&body=${encodedBody}`;
          window.open(fullUrl, '_blank');
          
          toast({
            title: "Copied to clipboard",
            description: "Message content ready to paste in Outlook",
          });
        })
        .catch((err) => {
          toast({
            title: "Failed to copy",
            description: "Please try copying manually",
            variant: "destructive",
          });
        });

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to prepare message content",
        variant: "destructive",
      });
    }
  };

  const featuredVendors = filteredVendors.filter(vendor => vendor.featured);
  const regularVendors = filteredVendors.filter(vendor => !vendor.featured);

  const togglePanel = () => {
    setIsPanelOpen(!isPanelOpen);
  };

  const selectedVendors = vendors.filter(vendor => selectedVendorIds.includes(vendor.id));

  const copyToClipboard = (ref: React.RefObject<HTMLInputElement | HTMLTextAreaElement>, fieldName: string) => {
    if (ref.current) {
      ref.current.select();
      document.execCommand('copy');
      
      // Set the appropriate state based on which field was copied
      if (fieldName === 'Email List') {
        setCopiedEmail(true);
        setTimeout(() => setCopiedEmail(false), 1000);
      } else if (fieldName === 'Subject') {
        setCopiedSubject(true);
        setTimeout(() => setCopiedSubject(false), 1000);
      } else if (fieldName === 'Message') {
        setCopiedMessage(true);
        setTimeout(() => setCopiedMessage(false), 1000);
      }
      
      toast({
        title: `${fieldName} copied to clipboard.`,
      });
    } else {
      toast({
        title: `Could not copy ${fieldName}.`,
        variant: 'destructive',
      });
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  useEffect(() => {
    // Define synonyms for common HEOR and market access terms
    const synonymGroups = [
      ["SLR", "evidence synthesis", "literature review"],
      ["Desk research", "literature review"],
      ["ITCs", "NMAs", "indirect treatment comparison", "network meta-analysis", "comparative efficacy"],
      ["burden of disease", "Epidemiology Studies"],
      ["RWE", "claims database analysis", "real-world outcomes study", "observational study", "retrospective data analysis", "registries"],
      ["evidence gap analysis", "data generation strategy", "integrated evidence planning", "access evidence", "payer evidence roadmap"],
      ["CEA model", "CEM", "health economic model", "cost-effectiveness analysis", "cost-effectiveness model"],
      ["BIM", "payer budget model", "financial impact assessment", "cost calculator", "budget impact model"],
      ["conceptual model", "HEOR feasibility model", "pre-launch economic evaluation", "early value model"],
      ["HTA model adaptation", "local market economic model", "regional health economic model", "country-specific model"],
      ["Global Value dossier", "AMCP Dossier", "global value communication", "payer dossier", "GVD"],
      ["payer value messaging", "value proposition", "payer communication strategy", "value frameworks"],
      ["payer objection tool", "Payer Q&A", "Payor Q&A", "HEOR Q&A", "value defense materials"],
      ["payer deck development", "market access slide support", "HEOR presentation materials", "custom payer slides"],
      ["reimbursement landscape analysis", "global access mapping", "market access research", "HTA requirements summary", "P&R landscape"],
      ["pricing strategy", "P&R launch support", "value-based pricing", "reimbursement"],
      ["HTA dossier preparation", "submission", "HTA strategy", "Health Technology Assessment", "HTA submission", "HTA readiness"],
      ["payer advisory board facilitation", "market access insights", "qualitative payer research", "payer interviews", "market research", "Payer Research"],
      ["HEOR launch plan", "HEOR strategy", "market access planning support", "launch readiness roadmap", "payer engagement plan"],
      ["payer stakeholder mapping", "key access influencer strategy", "decision-maker engagement", "stakeholder targeting plan"],
      ["HEOR training content", "internal access enablement", "payer communication training", "market access team upskilling", "team development"]
    ];
    
    // Build a map of terms to their synonyms
    const map: Record<string, string[]> = {};
    synonymGroups.forEach(group => {
      group.forEach(term => {
        map[term.toLowerCase()] = group.map(syn => syn.toLowerCase());
      });
    });
    
    setSynonymMap(map);
  }, []);

  useEffect(() => {
    // Start with all vendors as the base
    let baseResults = vendors;
    let finalResults = vendors;
    
    // Step 1: Apply publication search filter if active
    if (pubSearchVendors.length > 0) {
      const normalizeName = (s: string) =>
        s
          .toLowerCase()
          .replace(/[^a-z0-9]+/gi, ' ')
          .trim();

      baseResults = vendors.filter(vendor =>
        pubSearchVendors.some(name =>
          normalizeName(vendor.name) === normalizeName(name)
        )
      );
    } else if (pubSearchVendors.length === 0 && pubSearchLoading) {
      // Only show no vendors if we've actually run a search and got no results
      baseResults = [];
    }
    
    // Save the base filtered results (after publication filtering)
    setBaseFilteredVendors(baseResults);
    finalResults = baseResults;
    
    // Step 2: Apply agency name or services filter on top of the base results
    if (searchType === 'name' && searchQuery) {
      const searchTerm = searchQuery.toLowerCase();
      finalResults = finalResults.filter(vendor =>
        vendor.name.toLowerCase().includes(searchTerm)
      );
    } else if (searchType === 'services' && servicesSearchQuery) {
      const searchTerms = servicesSearchQuery.toLowerCase().split(' ').filter(term => term.length > 0);
      
      finalResults = finalResults.filter(vendor => {
        if (!vendor.services) return false;
        const vendorServices = vendor.services.toLowerCase();
        
        return searchTerms.every(term => {
          // Check for direct match
          if (vendorServices.includes(term)) return true;
          
          // Check for synonym matches
          const synonyms = synonymMap[term] || [];
          return synonyms.some(synonym => vendorServices.includes(synonym));
        });
      });
    } else if (searchType === 'dataProvider') {
      // Filter to only show data providers
      finalResults = finalResults.filter(vendor => vendor.dataProvider);
    }

    // Set the final filtered vendors
    setFilteredVendors(finalResults);
    
  }, [searchQuery, servicesSearchQuery, vendors, synonymMap, pubSearchVendors, activeSearchFields, searchType]);

  useEffect(() => {
    // This effect only runs when publication search state changes
    if (activeSearchFields.length > 0 && pubSearchVendors.length === 0 && pubSearchLoading === false) {
      // Don't clear vendors if we haven't run a search yet
      // Only clear if we've run a search and got no results
      return;
    }
  }, [activeSearchFields, pubSearchVendors, pubSearchLoading]);

  useEffect(() => {
    // Function to fetch advertisement images
    const fetchAdImages = async () => {
      try {
        const response = await fetch('/api/ads');
        if (response.ok) {
          const data = await response.json();
          const images = data.images || [];
          setAdImages(images);
          
          // Create a shuffled version of the images
          const shuffled = shuffleArray(images);
          
          // Fill up to 5 slots by repeating images if needed
          const minAdCount = 4;
          let filledAds = [...shuffled];
          while (filledAds.length < minAdCount && shuffled.length > 0) {
            filledAds = [...filledAds, ...shuffleArray(shuffled)];
          }
          
          setShuffledAds(filledAds.slice(0, minAdCount));
          console.log('Loaded ad images:', images);
        } else {
          console.error('Failed to fetch ad images');
        }
      } catch (error) {
        console.error('Error fetching ad images:', error);
      }
    };

    fetchAdImages();
  }, []);

  const handleApplyTemplate = (templateMessage: string, templateSubject?: string) => {
    setMessage(templateMessage);
    
    // Set the subject if provided
    if (templateSubject) {
      setSubject(templateSubject);
      
      // Adjust subject textarea height after setting content
      setTimeout(() => {
        if (subjectRef.current) {
          adjustTextareaHeight(subjectRef.current);
        }
      }, 0);
    }
    
    // Adjust message textarea height after setting content
    setTimeout(() => {
      if (messageRef.current) {
        adjustTextareaHeight(messageRef.current);
      }
    }, 0);
    
    console.log("Applied template with subject:", templateSubject);
  };

  // Define tutorial steps
  const tutorialSteps = [
    {
      id: 1,
      title: "Welcome to Evicenter!",
      content: "This quick tutorial will help you get started with finding and contacting agencies. Click 'Next' to continue.",
    },
    {
      id: 2,
      title: "Search for Agencies",
      content: "Use the search box to find agencies by name or filter by the services they offer.",
      targetId: "search-box",
    },
    {
      id: 3,
      title: "Select Agencies",
      content: "Click the checkbox on agency cards to select them for messaging.",
      targetId: "vendor-card",
    },
    {
      id: 4,
      title: "View Services",
      content: "Click the Services button to see detailed services offered by each agency.",
      targetId: "services-button",
    },
    {
      id: 5,
      title: "Compose Messages",
      content: "Use the message panel to write your message to selected agencies.",
      targetId: "message-panel-header", // Changed from "message-panel" to target the header instead
    },
    {
      id: 6,
      title: "Use Templates",
      content: "Save time by using the message template for common inquiries.",
      targetId: "template-button",
    },
    {
      id: 7,
      title: "You're All Set!",
      content: "You now know the basics of using VendorHub. Click 'Got it!' to start exploring, or click the help button anytime to see this tutorial again.",
    },
  ];

  // Add this state to track which vendors have services open
  const [openServiceVendors, setOpenServiceVendors] = useState<Set<string>>(new Set());

  // Add this function to handle toggling services visibility
  const toggleVendorServices = useCallback((vendorId: string, isOpen: boolean) => {
    setOpenServiceVendors(prev => {
      const updated = new Set(prev);
      if (isOpen) {
        updated.add(vendorId);
      } else {
        updated.delete(vendorId);
      }
      return updated;
    });
  }, []);

  // Add state to track if the button was recently clicked
  const [showCloseServicesCheckIcon, setShowCloseServicesCheckIcon] = useState(false);
  const [showDeselectCheckIcon, setShowDeselectCheckIcon] = useState(false);

  // Add this function to close all services
  const closeAllServices = useCallback(() => {
    setOpenServiceVendors(new Set());
    // Show check icon
    setShowCloseServicesCheckIcon(true);
    // Reset back to list icon after 1 second
    setTimeout(() => {
      setShowCloseServicesCheckIcon(false);
    }, 1000);
  }, []);

  // Add this function to deselect all vendors
  const deselectAllVendors = useCallback(() => {
    setSelectedVendorIds([]);
    setEmailList('');
    
    // Show check icon temporarily
    setShowDeselectCheckIcon(true);
    setTimeout(() => {
      setShowDeselectCheckIcon(false);
    }, 1000);
      
    console.log("Deselected all vendors");

  }, []);

  // Add these new state variables at the top with other state declarations
  const [isCompanyEmailDialogOpen, setIsCompanyEmailDialogOpen] = useState(false);
  const [isSendingEmails, setIsSendingEmails] = useState(false);
  const [sendingProgress, setSendingProgress] = useState(0);
  const [userInfo, setUserInfo] = useState({
    name: '',
    email: '',
  });

  // Add this function to handle user info input changes
  const handleUserInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserInfo(prev => ({ ...prev, [name]: value }));
  };

  // Add this function to handle sending emails via company email
  const handleSendViaCompanyEmail = async () => {
    try {
      // Validate inputs
      if (!emailList.trim()) {
        toast({
          title: "No recipients selected",
          description: "Please add at least one email address",
          variant: "destructive",
        });
        return;
      }

      if (!subject.trim() || !message.trim()) {
        toast({
          title: "Missing content",
          description: "Please provide both subject and message content",
          variant: "destructive",
        });
        return;
      }

      setIsSendingEmails(true);
      
      // Get all recipient emails
      const recipientEmails = emailList
        .split(/[,;]\s*/)
        .filter(email => email.trim());
      
      try {
        // Call the API endpoint
        const response = await fetch('/api/send-email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            recipients: recipientEmails,
            subject,
            message,
            userEmail: userInfo.email || null,
            userName: userInfo.name || null,
          }),
        });
        
        const result = await response.json();
        
        if (!response.ok) {
          throw new Error(result.error || 'Failed to send emails');
        }
        
        setIsCompanyEmailDialogOpen(false);
        setIsSendingEmails(false);
        
        // Show success/failure information
        if (result.results.successful.length > 0) {
          toast({
            title: "Emails sent successfully",
            description: `Sent ${result.results.successful.length} email${result.results.successful.length > 1 ? 's' : ''} on your behalf`,
          });
        }
        
        if (result.results.failed.length > 0) {
          toast({
            title: "Some emails failed to send",
            description: `${result.results.failed.length} email${result.results.failed.length > 1 ? 's' : ''} could not be sent`,
            variant: "destructive",
          });
        }
      } catch (error) {
        throw error;
      }
    } catch (error) {
      console.error("Error sending emails:", error);
      setIsSendingEmails(false);
      
      toast({
        title: "Failed to send emails",
        description: error instanceof Error ? error.message : "There was an error sending your emails",
        variant: "destructive",
      });
    }
  };

  // Add this function to the Home component
  const handlePubSearch = async () => {
    setPubSearchLoading(true);
    
    try {
      // Reset all search parameters
      const params = new URLSearchParams();
      
      // Add current search query if it exists
      if (pubSearchType !== 'all' && pubSearchQuery.trim()) {
        params.append(pubSearchType, pubSearchQuery);
        
        // Also add to active fields for display
        const newFields = {...pubSearchFields, [pubSearchType]: pubSearchQuery.trim()};
        setPubSearchFields(newFields);
        if (!activeSearchFields.includes(pubSearchType)) {
          setActiveSearchFields([...activeSearchFields, pubSearchType]);
        }
      } else if (pubSearchType === 'all' && pubSearchQuery.trim()) {
        // Search all fields if "all" is selected
        params.append('all', pubSearchQuery);
      }
      
      // Add all active search fields - each field is an AND condition
      for (const field of activeSearchFields) {
        if (pubSearchFields[field].trim()) {
          params.append(field, pubSearchFields[field]);
        }
      }
      
      // If no search parameters, return early
      if (params.toString() === '') {
        setPubSearchLoading(false);
        toast({
          title: "No Search Criteria",
          description: "Please enter at least one search term",
          variant: "destructive",
        });
        return;
      }
      
      console.log(`Searching publications with AND criteria: ${params.toString()}`);
      
      const response = await fetch(`/api/search-publications-vendors?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to search publications');
      }
      
      const data = await response.json();
      console.log('Search results:', data);
      
      // Set the pubSearchVendors state - this will trigger the useEffect
      setPubSearchVendors(data.vendorNames || []);
      
      // Toast notifications based on results
      if (!data.vendorNames || data.vendorNames.length === 0) {
        if (data.publicationsCount > 0) {
          toast({
            title: "Publications Found",
            description: `Found ${data.publicationsCount} publications, but couldn't match to specific vendors`,
            variant: "default",
          });
        } else {
          toast({
            title: "No Results",
            description: "No publications found matching your search",
            variant: "destructive",
          });
        }
      } else {
        toast({
          title: "Search Results",
          description: `Found ${data.publicationsCount} publications from ${data.vendorNames.length} vendors`,
        });
      }
    } catch (error) {
      console.error('Error searching publications:', error);
      toast({
        title: "Error",
        description: "Failed to search publications",
        variant: "destructive",
      });
      // Reset publication search vendors on error
      setPubSearchVendors([]);
    } finally {
      setPubSearchLoading(false);
    }
  };

  // Update this function to reset all publication search fields
  const resetPubSearch = () => {
    setPubSearchQuery('');
    setPubSearchVendors([]);
    setPubSearchFields({
      title: '',
      abstract: '',
      therapeuticArea: '',
      region: '',
      products: '',
      studyType: ''
    });
    setActiveSearchFields([]);
    // Let the useEffect handle the filtering based on any remaining agency search
  };

  // Add this helper function to format search field names
  const formatSearchFieldName = (fieldName: string): string => {
    // Convert camelCase to space-separated words
    // First, add a space before each capital letter and convert to lowercase
    const formatted = fieldName.replace(/([A-Z])/g, ' $1').toLowerCase();
    // Then capitalize the first letter of the result
    return formatted.charAt(0) + formatted.slice(1);
  };

  return (<div className="flex min-h-screen relative">
      {/* Left Panel for Advertisements - hide on mobile */}
      {!isMobile && (
        <div 
          className="bg-secondary border-r p-4 flex-shrink-0 -mt-4 relative hidden md:block"
          style={{ width: `${leftPanelWidth}px` }}
        >
          {/* Resize Handle for left panel */}
          <div
            ref={leftResizeHandleRef}
            className="absolute right-0 top-0 w-1 h-full cursor-ew-resize hover:bg-accent/50 transition-colors"
            onMouseDown={handleLeftMouseDown}
            style={{ touchAction: 'none' }}
          />
        
          <h2 className="text-md font-medium mb-2 mt-2 text-muted-foreground">Sponsored</h2>
          <div className="space-y-6">
            {adImages.length > 0 ? (
              shuffledAds.map((image, index) => (
                <div key={index} className="mb-4 rounded-lg overflow-hidden border border-border/50 hover:border-border transition-colors">
                  <img
                    src={`/ads/${image}`}
                    alt={`Advertisement ${index + 1}`}
                    className="w-full aspect-auto object-contain"
                  />
                </div>
              ))
            ) : (
              <div className="space-y-4">
                <div className="animate-pulse w-full aspect-video bg-gray-200 rounded-md"></div>
                <div className="animate-pulse w-full aspect-video bg-gray-200 rounded-md"></div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Main Content Area - will shrink to accommodate the right panel */}
      <div className="flex-1 overflow-hidden">
        <main className="flex flex-col h-screen" style={{ 
          width: !isMobile && isPanelOpen ? `calc(100% - ${panelWidth}px)` : '100%',
          transition: 'width 0.2s ease-in-out'
        }}>
          {/* Sticky Header Section - fixed positioning */}
          <div 
            className="sticky top-0 z-20 bg-background border-b border-gray-100 dark:border-gray-800 pt-4 px-4 pb-2"
            style={{ position: 'sticky', top: 0 }}
          >
            {/* React Toaster */}
            <Toaster />

            {/* Logo and action buttons in the same row */}
            <div className="flex justify-between items-center mb-0 -mt-8">
              <div className="flex-shrink-0 cursor-pointer" 
                onClick={() => window.location.reload()}
                title="Reload page"
              >
                <Image
                  src={theme === 'dark' ? EvicenterWhiteLogo : EvicenterLogo}
                  alt="Evicenter Logo"
                  width={210}
                  height={200}
                  className="h-auto mt-1 -mb-3 ml-1"
                />
              </div>
              
              {/* Action buttons - positioned based on panel state, hidden on mobile */}
              {!isMobile && (
                <div className={`flex items-center ${isPanelOpen ? 'gap-2' : 'gap-2 mr-44'}`}>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={deselectAllVendors}
                    className="text-xs border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-900"
                  >
                    {showDeselectCheckIcon ? (
                      <Check className="h-4 w-4 mr-1" />
                    ) : (
                      <X className="h-4 w-4 mr-1" />
                    )}
                    Deselect All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={closeAllServices}
                    className="text-xs border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-900"
                    id="services-button"
                  >
                    {showCloseServicesCheckIcon ? (
                      <Check className="h-4 w-4 mr-1" />
                    ) : (
                      <ListChecks className="h-4 w-4 mr-1" />
                    )}
                    Close All Services
                  </Button>
                </div>
              )}
            </div>

            {/* Combined search bar with dropdown */}
            <div className="mb-4 w-full">
              <Tabs defaultValue="agency" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="agency">Search Agencies</TabsTrigger>
                  <TabsTrigger value="publications">Search Publications</TabsTrigger>
                </TabsList>
                
                <TabsContent value="agency" className="mt-2">
                  <div className="flex gap-2">
                    <Select 
                      defaultValue="name" 
                      onValueChange={(value: 'name' | 'services' | 'dataProvider') => {
                        // Clear the search query when switching search types
                        setSearchQuery('');
                        setServicesSearchQuery('');
                        setSearchType(value);
                      }}
                    >
                      <SelectTrigger className="w-[170px]">
                        <SelectValue placeholder="Search by..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="name">Agency Name</SelectItem>
                        <SelectItem value="services">Services Offered</SelectItem>
                        <SelectItem value="dataProvider">Data Providers</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Input
                      type="search"
                      placeholder={
                        searchType === 'services' 
                          ? "Search for services..." 
                          : searchType === 'dataProvider'
                            ? "Showing data providers only"
                            : "Search agencies..."
                      }
                      value={searchQuery || servicesSearchQuery}
                      onChange={(e) => {
                        const value = e.target.value;
                        // Update the appropriate state based on the selected search type
                        if (searchType === 'name') {
                          setSearchQuery(value);
                          setServicesSearchQuery('');
                        } else if (searchType === 'services') {
                          setServicesSearchQuery(value);
                          setSearchQuery('');
                        } else {
                          // For dataProvider, we don't need a search query
                          setSearchQuery('');
                          setServicesSearchQuery('');
                        }
                      }}
                      className="flex-1 min-w-0"
                      id="search-box"
                      disabled={searchType === 'dataProvider'}
                    />
                  </div>
                </TabsContent>
                
                <TabsContent value="publications" className="mt-2">
                  <div className="flex flex-col gap-2">
                    {/* Active search fields display */}
                    {activeSearchFields.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2">
                        {activeSearchFields.map(field => (
                          <Badge 
                            key={field} 
                            variant="outline" 
                            className="flex items-center gap-1.5 bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800 py-1.5 px-2.5 rounded-full"
                          >
                            <span className="font-medium">{formatSearchFieldName(field)}:</span>
                            <span className="text-amber-700 dark:text-amber-300">{pubSearchFields[field]}</span>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-4 w-4 p-0 ml-1 hover:bg-transparent" 
                              onClick={() => {
                                const newFields = {...pubSearchFields, [field]: ''};
                                setPubSearchFields(newFields);
                                setActiveSearchFields(activeSearchFields.filter(f => f !== field));
                              }}
                            >
                              <X className="h-3 w-3 text-amber-500 hover:text-amber-700 dark:hover:text-amber-300" />
                            </Button>
                          </Badge>
                        ))}
                        {activeSearchFields.length > 0 && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-xs text-muted-foreground hover:text-amber-600 dark:hover:text-amber-300 flex items-center gap-1" 
                            onClick={() => {
                              setPubSearchFields({
                                title: '',
                                abstract: '',
                                therapeuticArea: '',
                                region: '',
                                products: '',
                                studyType: ''
                              });
                              setActiveSearchFields([]);
                            }}
                          >
                            <X className="h-3.5 w-3.5" />
                            Clear all
                          </Button>
                        )}
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Select 
                        value={pubSearchType}
                        onValueChange={(value) => {
                          setPubSearchType(value);
                          setPubSearchQuery('');
                        }}
                      >
                        <SelectTrigger className="w-[170px]">
                          <SelectValue placeholder="Search in..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Fields</SelectItem>
                          <SelectItem value="title">Title</SelectItem>
                          <SelectItem value="abstract">Abstract</SelectItem>
                          <SelectItem value="therapeuticArea">Therapeutic Area</SelectItem>
                          <SelectItem value="region">Region</SelectItem>
                          <SelectItem value="products">Products</SelectItem>
                          <SelectItem value="studyType">Study Type</SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <div className="flex-1 flex gap-2">
                        <Input
                          type="search"
                          placeholder={`Search in ${pubSearchType === 'all' ? 'all fields' : formatSearchFieldName(pubSearchType)}...`}
                          value={pubSearchQuery}
                          onChange={(e) => setPubSearchQuery(e.target.value)}
                          className="flex-1 min-w-0"
                        />
                        
                        <Button 
                          onClick={() => {
                            if (pubSearchType !== 'all' && pubSearchQuery.trim()) {
                              // Add this field to the active search fields
                              const newFields = {...pubSearchFields, [pubSearchType]: pubSearchQuery.trim()};
                              setPubSearchFields(newFields);
                              if (!activeSearchFields.includes(pubSearchType)) {
                                setActiveSearchFields([...activeSearchFields, pubSearchType]);
                              }
                              setPubSearchQuery(''); // Clear the input after adding
                            } else {
                              // If "all" is selected, just run the search
                              handlePubSearch();
                            }
                          }}
                          disabled={!pubSearchQuery.trim()}
                          variant="outline"
                          className="border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-900"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                        
                        <Button 
                          onClick={handlePubSearch} 
                          disabled={pubSearchLoading || (pubSearchQuery.trim() === '' && activeSearchFields.length === 0)}
                          className="bg-amber-400 text-black hover:bg-amber-500"
                        >
                          {pubSearchLoading ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
                          ) : (
                            <Search className="h-4 w-4" />
                          )}
                        </Button>
                        
                        {(pubSearchVendors.length > 0 || activeSearchFields.length > 0) && (
                          <Button 
                            variant="outline" 
                            onClick={resetPubSearch}
                            className="border-amber-500 text-amber-600 hover:bg-amber-50 hover:text-amber-900"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {pubSearchVendors.length > 0 && (
                      <div className="text-xs text-muted-foreground mt-1">
                        Showing {filteredVendors.length} agencies with publications matching your search criteria
                        {filteredVendors.length === 0 && pubSearchVendors.length > 0 && (
                          <span className="text-amber-500 ml-1">
                            (Note: Some agencies in the database may not match exactly with our agency list)
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Scrollable Content Section */}
          <div className="flex-1 overflow-y-auto p-4 pt-4">
            <section>
              <h2 className="text-xl font-semibold mb-4">All Agencies ({filteredVendors.length})</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredVendors.length > 0 ? (
                  filteredVendors.map((vendor) => (
                    <VendorCard
                      key={vendor.id}
                      vendor={vendor}
                      selectedVendorIds={selectedVendorIds}
                      toggleVendorSelection={toggleVendorSelection}
                      servicesSearchQuery={servicesSearchQuery}
                      openServiceVendors={openServiceVendors}
                      toggleVendorServices={toggleVendorServices}
                      synonymMap={synonymMap}
                      globalPubSearchQuery={pubSearchQuery}
                      globalPubSearchType={pubSearchType}
                      globalPubSearchFields={pubSearchFields}
                    />
                  ))
                ) : (
                  <p>No agencies to display</p>
                )}
              </div>
            </section>
          </div>
        </main>
      </div>

      {/* Right Panel - hide on mobile */}
      {isPanelOpen && !isMobile && (
        <>
          <div 
            className="fixed top-0 right-0 h-full bg-secondary border-l overflow-y-auto flex flex-col pl-4"
            style={{ width: `${panelWidth}px` }}
          >
            {/* Resize Handle */}
            <div
              ref={resizeHandleRef}
              className="absolute left-0 top-0 w-1 h-full cursor-ew-resize hover:bg-accent/50 transition-colors"
              onMouseDown={handleMouseDown}
              style={{ touchAction: 'none' }}
            />
            
            {/* Message Panel Header */}
            <div className="flex justify-between items-center -mb-5 p-4" data-tip-target="message-panel-header">
              <h2 className="text-2xl font-bold tracking-tight">
                <span className="text-black dark:text-white">Compose</span>
                <span className="text-amber-500">/Message</span>
              </h2>
              <div className="flex items-center gap-2">
                <ThemeToggle />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={togglePanel}
                        className="hover:bg-transparent group"
                      >
                        <PanelRight className="h-4 w-4 group-hover:text-amber-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      Hide message panel
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            {/* Message Composition Area */}
            <div className="flex-1 p-4 mb-4" data-tip-target="message-panel">
              <div className="relative mb-2">
                <Textarea
                  placeholder="To..."
                  value={emailList}
                  onChange={handleEmailListChange}
                  className="pr-10 min-h-[40px] resize-none"
                  style={{ width: 'calc(100% - 40px)' }}
                  ref={emailListRef}
                  rows={1}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-0 hover:bg-transparent group"
                  onClick={() => copyToClipboard(emailListRef, 'Email list')}
                >
                  {copiedEmail ? 
                    <Check className="h-4 w-4 text-amber-500" /> : 
                    <Copy className="h-4 w-4 group-hover:text-amber-500" />
                  }
                </Button>
              </div>

              <div className="relative mb-2">
                <Textarea
                  placeholder="Write the subject of your message here..."
                  value={subject}
                  onChange={handleSubjectChange}
                  className="pr-10 min-h-[40px] resize-none"
                  style={{ width: 'calc(100% - 40px)' }}
                  ref={subjectRef}
                  rows={1}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = `${target.scrollHeight}px`;
                  }}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-1 top-0 hover:bg-transparent group"
                  onClick={() => copyToClipboard(subjectRef, 'Subject')}
                >
                  {copiedSubject ? 
                    <Check className="h-4 w-4 text-amber-500" /> : 
                    <Copy className="h-4 w-4 group-hover:text-amber-500" />
                  }
                </Button>
              </div>

              <div className="relative">
                <Textarea
                  placeholder="Write your message here..."
                  value={message}
                  onChange={handleMessageChange}
                  className="resize-none mb-4 pr-10"
                  style={{ 
                    width: 'calc(100% - 40px)',
                    minHeight: '200px'
                  }}
                  ref={messageRef}
                  onInput={(e) => {
                    const target = e.target as HTMLTextAreaElement;
                    target.style.height = 'auto';
                    target.style.height = `${Math.max(200, target.scrollHeight)}px`;
                  }}
                />
                <div className="absolute right-1 top-0 flex flex-col gap-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-transparent group"
                          onClick={() => copyToClipboard(messageRef, 'Message')}
                        >
                          {copiedMessage ? 
                            <Check className="h-4 w-4 text-amber-500" /> : 
                            <Copy className="h-4 w-4 group-hover:text-amber-500" />
                          }
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Copy text</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="hover:bg-transparent group"
                          onClick={() => setIsTemplateFormOpen(true)}
                          data-tip-target="template-button"
                        >
                          <ClipboardList className={cn(
                            "h-4 w-4",
                            theme === 'dark' 
                              ? "group-hover:text-amber-500" 
                              : "text-black group-hover:text-black"
                          )} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Use message templates</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>

              <div className="flex flex-col gap-2">
                {/* AI Assistant Icons */}
                <div className="flex items-center gap-4 mb-2">
                  <p className="text-sm text-muted-foreground -mb-2">Generate with AI:</p>
                </div>
                <div className="flex items-center gap-4 mb-4">
                  {/* Use theme to conditionally render icons */}
                  {(() => {
                    const { theme } = useTheme();
                    return (
                      <>
                        <a 
                          href="https://chatgpt.com/" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:opacity-80 transition-opacity"
                        >
                          <Image 
                            src={theme === 'dark' ? OpenAILogoWhite : OpenAILogo} 
                            alt="ChatGPT" 
                            width={24} 
                            height={24} 
                            className="h-6 w-6"
                          />
                        </a>
                        <a 
                          href="https://claude.ai/" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:opacity-80 transition-opacity"
                        >
                          <Image 
                            src={ClaudeAILogo} 
                            alt="Claude AI" 
                            width={24} 
                            height={24} 
                            className="h-6 w-6"
                          />
                        </a>
                        <a 
                          href="https://gemini.google.com/app" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:opacity-80 transition-opacity"
                        >
                          <Image 
                            src={GoogleGeminiLogo} 
                            alt="Google Gemini" 
                            width={24} 
                            height={24} 
                            className="h-6 w-6"
                          />
                        </a>
                        <a 
                          href="https://www.perplexity.ai/" 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="hover:opacity-80 transition-opacity"
                        >
                          <Image 
                            src={theme === 'dark' ? PerplexityLogoWhite : PerplexityLogo} 
                            alt="Perplexity" 
                            width={24} 
                            height={24} 
                            className="h-6 w-6"
                          />
                        </a>
                      </>
                    );
                  })()}
                </div>

                <Button 
                  onClick={handleOpenNativeOutlook} 
                  className="bg-amber-400 text-black hover:bg-amber-500 text-md" 
                  style={{ width: 'calc(100% - 40px)' }}
                >
                  Open in Native App
                </Button>
                <Button 
                  onClick={handleOpenOutlookWebApp} 
                  className="bg-amber-400 text-black hover:bg-amber-500 text-md" 
                  style={{ width: 'calc(100% - 40px)' }}
                >
                  Open Outlook (Web)
                </Button>
                <Button 
                  onClick={() => setIsCompanyEmailDialogOpen(true)} 
                  className="bg-amber-500 text-black hover:bg-amber-600 text-md" 
                  style={{ width: 'calc(100% - 40px)' }}
                >
                  Send via Evicenter
                </Button>
              </div>

              {/* Company Email Dialog */}
              <Dialog open={isCompanyEmailDialogOpen} onOpenChange={setIsCompanyEmailDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Send via Evicenter</DialogTitle>
                    <DialogDescription>
                      We'll send emails to the selected vendors on your behalf.
                      {selectedVendors.length > 1 && (
                        <p className="mt-2 text-amber-500 font-medium">
                          Note: This will send {selectedVendors.length} separate emails (one per vendor).
                        </p>
                      )}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="name" className="text-right">
                        Your Name
                      </Label>
                      <Input
                        id="name"
                        name="name"
                        value={userInfo.name}
                        onChange={handleUserInfoChange}
                        className="col-span-3"
                        placeholder="Optional"
                      />
                    </div>
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="email" className="text-right">
                        Your Email
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={userInfo.email}
                        onChange={handleUserInfoChange}
                        className="col-span-3"
                        placeholder="For replies (optional)"
                      />
                    </div>
                    {isSendingEmails && (
                      <div className="mt-2 text-center">
                        <p className="text-sm">Sending emails...</p>
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsCompanyEmailDialogOpen(false)} disabled={isSendingEmails}>
                      Cancel
                    </Button>
                    <Button onClick={handleSendViaCompanyEmail} disabled={isSendingEmails} className="bg-amber-400 text-black hover:bg-amber-500">
                      {isSendingEmails ? 'Sending...' : 'Send Emails'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>

              {/* Display Selected Vendors */}
              <div className="mt-10">
                <h3 className="text-lg font-semibold mb-2 text-black-500">Selected Agencies</h3>
                {selectedVendors.length > 0 ? (
                  <ul>
                    {selectedVendors.map(vendor => (
                      <li key={vendor.id} className="mb-0.5 flex items-center justify-between px-3 py-1.5 hover:bg-amber-500/10 rounded-md transition-colors group">
                        <span className="text-base text-foreground">
                          {vendor.name}
                        </span>
                        <button
                          onClick={() => toggleVendorSelection(vendor.id)}
                          className="hover:bg-amber-500/20 p-1 rounded transition-colors"
                        >
                          <X className="h-4 w-4 text-muted-foreground group-hover:text-amber-500" />
                        </button>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-muted-foreground">No agencies selected.</p>
                )}
              </div>
            </div>

            {/* Navigation Links - Two columns */}
            {/* TODO: Update links to correct pages */}
            <div className="mt-auto ml-4 mr-4 pt-4 border-t">
              <nav className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-muted-foreground">
                <div className="flex flex-col gap-2">
                  <a href="/about" className="hover:text-foreground transition-colors">
                    About
                  </a>
                  {/*<a href="/reg-hta-updates" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    News
                  </a>
                   {/*<a href="/ema-rwd-catalogue" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    EMA RWD Catalogue
                  </a>
                  {/*<a href="/advertise" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    Advertise
                  </a>
                  {/*<a href="/hta-reports" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    HTA Reports
                  </a>
                  {/*<a href="/clinical-trials" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                   CT.gov Search
                  </a>
                </div>
                <div className="flex flex-col gap-2">
                  {/*<a href="/drug-docs" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    Drugs@FDA Search
                  </a>
                  {/*<a href="/pubmed-explorer" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    PubMed Search
                  </a>
                  {/*<a href="/europe-pmc-explorer" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    Europe PMC Search
                  </a>
                  {/*<a href="/medicines-explorer" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    Medicines Explorer
                  </a>
                  {/*<a href="/drug-label-comparator" className="hover:text-foreground transition-colors">*/}
                  <a href="/about" className="hover:text-foreground transition-colors">
                    Drug Label Comparator
                  </a>
                  <a href="/featured-vendors" className="hover:text-foreground transition-colors">
                    Featured Agencies
                  </a>
                  <a href="/policies" className="hover:text-foreground transition-colors">
                    Policies
                  </a>
                  <a href="/help-contact" className="hover:text-foreground transition-colors">
                    Help & Contact
                  </a>
                </div>
              </nav>
              
              {/* Copyright Notice */}
              <div className="mt-4 mb-4 pt-4 border-t text-xs text-muted-foreground text-left">
                Advection LLC © 2025. All Rights Reserved.
              </div>
            </div>
          </div>
        </>
      )}

      {/* Message Panel Button - only shown when panel is closed and not on mobile */}
      {!isPanelOpen && !isMobile && (
        <div className="fixed top-4 right-4 z-50">
          <Button 
            onClick={togglePanel} 
            className="bg-amber-400 text-black hover:bg-amber-500 px-3"
          >
            <PanelRight className="mr-1 h-5 w-5" />
            Message Panel
          </Button>
        </div>
      )}

      <MessageTemplateForm 
        open={isTemplateFormOpen} 
        onOpenChange={setIsTemplateFormOpen} 
        onApplyTemplate={(message, subject) => handleApplyTemplate(message, subject)} 
      />

      {/* Add the tutorial widget */}
      <TutorialWidget steps={tutorialSteps} />
      
      {/* Chatbot */}
      <Chatbot />
    </div>
  );
}


