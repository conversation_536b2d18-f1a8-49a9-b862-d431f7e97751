"use client";

import React, { useState, useMemo, useEffect } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Filter,
  ExternalLink,
  Bookmark,
  BookmarkCheck,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { ThemeToggle } from "@/components/ThemeToggle";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip as ReTooltip } from "recharts";
import medicinesData from "../../../medicines_output_medicines_en_fixed.json";

interface Medicine {
  [key: string]: any;
  Category: string;
  "Name of medicine": string;
  "Medicine status": string;
  "International non-proprietary name (INN) / common name"?: string;
  "Active substance"?: string;
  "Therapeutic area (MeSH)"?: string;
  "Therapeutic indication"?: string;
  "Marketing authorisation date"?: string;
  "Medicine URL"?: string;
}

export default function MedicinesExplorerPage() {
  const medicines = medicinesData as Medicine[];

  const statuses = useMemo(
    () => Array.from(new Set(medicines.map((m) => m["Medicine status"]))),
    [medicines],
  );
  const categories = useMemo(
    () => Array.from(new Set(medicines.map((m) => m.Category))),
    [medicines],
  );
  const opinionStatuses = useMemo(
    () => Array.from(new Set(medicines.map((m) => m["Opinion status"]))).filter(Boolean),
    [medicines],
  );
  const therapeuticAreas = useMemo(
    () =>
      Array.from(new Set(medicines.map((m) => m["Therapeutic area (MeSH)"]))).filter(
        Boolean,
      ),
    [medicines],
  );
  const pharmGroups = useMemo(
    () =>
      Array.from(new Set(medicines.map((m) => m["Pharmacotherapeutic group\n(human)"]))).filter(
        Boolean,
      ),
    [medicines],
  );
  const yesNoOptions = ["Yes", "No"];

  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [compareNames, setCompareNames] = useState<string[]>([]);
  const [favoriteNames, setFavoriteNames] = useLocalStorage<string[]>(
    "favoriteMedicines",
    [],
  );
  const [opinionStatusFilter, setOpinionStatusFilter] = useState("all");
  const [therapeuticAreaFilter, setTherapeuticAreaFilter] = useState("all");
  const [pharmGroupFilter, setPharmGroupFilter] = useState("all");
  const [acceleratedFilter, setAcceleratedFilter] = useState("all");
  const [advancedTherapyFilter, setAdvancedTherapyFilter] = useState("all");
  const [biosimilarFilter, setBiosimilarFilter] = useState("all");
  const [conditionalFilter, setConditionalFilter] = useState("all");
  const [exceptionalFilter, setExceptionalFilter] = useState("all");
  const [genericFilter, setGenericFilter] = useState("all");
  const [orphanFilter, setOrphanFilter] = useState("all");
  const [primeFilter, setPrimeFilter] = useState("all");

  const [openItems, setOpenItems] = useState<string[]>([]);
  const [pdfLinks, setPdfLinks] = useState<Record<string, string[]>>({});

  const filteredMedicines = useMemo(() => {
    return medicines.filter((m) => {
      const searchText = `${m["Name of medicine"]} ${m["International non-proprietary name (INN) / common name"] ?? ""} ${m["Active substance"] ?? ""}`.toLowerCase();
      const matchesSearch = searchText.includes(search.toLowerCase());
      const matchesStatus = statusFilter === "all" || m["Medicine status"] === statusFilter;
      const matchesCategory = categoryFilter === "all" || m.Category === categoryFilter;
      const matchesOpinion = opinionStatusFilter === "all" || m["Opinion status"] === opinionStatusFilter;
      const matchesTherapeutic =
        therapeuticAreaFilter === "all" || m["Therapeutic area (MeSH)"] === therapeuticAreaFilter;
      const matchesPharmGroup =
        pharmGroupFilter === "all" || m["Pharmacotherapeutic group\n(human)"] === pharmGroupFilter;
      const matchesAccelerated =
        acceleratedFilter === "all" || m["Accelerated assessment"] === acceleratedFilter;
      const matchesAdvanced =
        advancedTherapyFilter === "all" || m["Advanced therapy"] === advancedTherapyFilter;
      const matchesBiosimilar = biosimilarFilter === "all" || m.Biosimilar === biosimilarFilter;
      const matchesConditional =
        conditionalFilter === "all" || m["Conditional approval"] === conditionalFilter;
      const matchesExceptional =
        exceptionalFilter === "all" || m["Exceptional circumstances"] === exceptionalFilter;
      const matchesGeneric = genericFilter === "all" || m["Generic or hybrid"] === genericFilter;
      const matchesOrphan = orphanFilter === "all" || m["Orphan medicine"] === orphanFilter;
      const matchesPrime = primeFilter === "all" || m["PRIME: priority medicine"] === primeFilter;

      return (
        matchesSearch &&
        matchesStatus &&
        matchesCategory &&
        matchesOpinion &&
        matchesTherapeutic &&
        matchesPharmGroup &&
        matchesAccelerated &&
        matchesAdvanced &&
        matchesBiosimilar &&
        matchesConditional &&
        matchesExceptional &&
        matchesGeneric &&
        matchesOrphan &&
        matchesPrime
      );
    });
  }, [
    medicines,
    search,
    statusFilter,
    categoryFilter,
    opinionStatusFilter,
    therapeuticAreaFilter,
    pharmGroupFilter,
    acceleratedFilter,
    advancedTherapyFilter,
    biosimilarFilter,
    conditionalFilter,
    exceptionalFilter,
    genericFilter,
    orphanFilter,
    primeFilter,
  ]);

  const statusChartData = useMemo(() => {
    const counts: Record<string, number> = {};
    filteredMedicines.forEach((m) => {
      counts[m["Medicine status"]] = (counts[m["Medicine status"]] || 0) + 1;
    });
    return Object.entries(counts).map(([name, value]) => ({ name, value }));
  }, [filteredMedicines]);

  const favoriteMedicines = useMemo(
    () =>
      medicines.filter((m) =>
        favoriteNames.includes(m["Name of medicine"] as string),
      ),
    [favoriteNames, medicines],
  );

  const compareMedicines = useMemo(
    () =>
      medicines.filter((m) =>
        compareNames.includes(m["Name of medicine"] as string),
      ),
    [compareNames, medicines],
  );

  const toggleFavorite = (name: string) => {
    if (favoriteNames.includes(name)) {
      setFavoriteNames(favoriteNames.filter((n) => n !== name));
    } else {
      setFavoriteNames([...favoriteNames, name]);
    }
  };

  const toggleCompare = (name: string) => {
    setCompareNames((prev) =>
      prev.includes(name) ? prev.filter((n) => n !== name) : [...prev, name],
    );
  };

  function MedicineItem({ med, value, isOpen }: { med: Medicine; value: string; isOpen: boolean }) {
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      if (isOpen && med["Medicine URL"] && pdfLinks[value] === undefined) {
        setLoading(true);
        fetch(`/api/ema-pdfs?url=${encodeURIComponent(med["Medicine URL"]!)}`)
          .then((res) => res.json())
          .then((data) => {
            setPdfLinks((prev) => ({ ...prev, [value]: data.pdfs || [] }));
          })
          .catch((err) => {
            console.error('Failed to fetch PDFs', err);
            setPdfLinks((prev) => ({ ...prev, [value]: [] }));
          })
          .finally(() => setLoading(false));
      }
    }, [isOpen, med, value]);

    const docs = pdfLinks[value];

    return (
      <AccordionItem value={value}>
        <AccordionTrigger className="text-left">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <span className="font-medium">{med["Name of medicine"]}</span>
              <span className="text-sm text-muted-foreground">{med["Medicine status"]}</span>
            </div>
            <div className="flex items-center gap-2 ml-2" onClick={(e) => e.stopPropagation()}>
              <Checkbox
                checked={compareNames.includes(med["Name of medicine"])}
                onCheckedChange={() => toggleCompare(med["Name of medicine"])}
              />
              <button onClick={() => toggleFavorite(med["Name of medicine"])} aria-label="Favorite">
                {favoriteNames.includes(med["Name of medicine"]) ? (
                  <BookmarkCheck className="h-4 w-4 text-amber-500" />
                ) : (
                  <Bookmark className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent>
          <Card className="mb-4">
            <CardHeader>
              <CardTitle>{med["Name of medicine"]}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-2 text-sm">
                {med["International non-proprietary name (INN) / common name"] && (
                  <p>
                    <strong>INN:</strong> {med["International non-proprietary name (INN) / common name"]}
                  </p>
                )}
                {med["Active substance"] && (
                  <p>
                    <strong>Active substance:</strong> {med["Active substance"]}
                  </p>
                )}
                {med["Therapeutic area (MeSH)"] && (
                  <p>
                    <strong>Therapeutic area:</strong> {med["Therapeutic area (MeSH)"]}
                  </p>
                )}
                {med["Pharmacotherapeutic group\n(human)"] && (
                  <p>
                    <strong>Pharmacotherapeutic group:</strong> {med["Pharmacotherapeutic group\n(human)"]}
                  </p>
                )}
                {med["Therapeutic indication"] && (
                  <p>
                    <strong>Indication:</strong> {med["Therapeutic indication"]}
                  </p>
                )}
                {med["Marketing authorisation developer / applicant / holder"] && (
                  <p>
                    <strong>MAH:</strong> {med["Marketing authorisation developer / applicant / holder"]}
                  </p>
                )}
                {med["Opinion status"] && (
                  <p>
                    <strong>Opinion status:</strong> {med["Opinion status"]}
                  </p>
                )}
                {med["Accelerated assessment"] && (
                  <p>
                    <strong>Accelerated assessment:</strong> {med["Accelerated assessment"]}
                  </p>
                )}
                {med["Advanced therapy"] && (
                  <p>
                    <strong>Advanced therapy:</strong> {med["Advanced therapy"]}
                  </p>
                )}
                {med.Biosimilar && (
                  <p>
                    <strong>Biosimilar:</strong> {med.Biosimilar}
                  </p>
                )}
                {med["Conditional approval"] && (
                  <p>
                    <strong>Conditional approval:</strong> {med["Conditional approval"]}
                  </p>
                )}
                {med["Exceptional circumstances"] && (
                  <p>
                    <strong>Exceptional circumstances:</strong> {med["Exceptional circumstances"]}
                  </p>
                )}
                {med["Generic or hybrid"] && (
                  <p>
                    <strong>Generic or hybrid:</strong> {med["Generic or hybrid"]}
                  </p>
                )}
                {med["Orphan medicine"] && (
                  <p>
                    <strong>Orphan medicine:</strong> {med["Orphan medicine"]}
                  </p>
                )}
                {med["PRIME: priority medicine"] && (
                  <p>
                    <strong>PRIME:</strong> {med["PRIME: priority medicine"]}
                  </p>
                )}
                {med["Marketing authorisation date"] && (
                  <p>
                    <strong>MA date:</strong> {med["Marketing authorisation date"]}
                  </p>
                )}
                {med["Medicine URL"] && (
                  <p className="md:col-span-2">
                    <a
                      href={med["Medicine URL"]}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 underline inline-flex items-center"
                    >
                      More information <ExternalLink className="ml-1 h-4 w-4" />
                    </a>
                  </p>
                )}
              </div>
              <div className="mt-4">
                {loading && <p>Loading documents...</p>}
                {docs && (
                  <div>
                    <h4 className="font-medium mb-2">Documents</h4>
                    {docs.length > 0 ? (
                      <ul className="list-disc pl-5 space-y-1">
                        {docs.map((link, idx) => (
                          <li key={idx}>
                            <a
                              href={link}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-amber-600 underline inline-flex items-center"
                            >
                              {link.split('/').pop()}
                              <ExternalLink className="ml-1 h-4 w-4" />
                            </a>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm">No documents found.</p>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </AccordionContent>
      </AccordionItem>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <Link href="/">
          <Button variant="ghost" className="pl-0 hover:bg-transparent">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        </Link>
        <ThemeToggle />
      </div>

      <h1 className="text-3xl font-bold mb-6">EMA Medicines Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>
          Explore authorised medicines and related information. Use the search
          and filters to quickly find medicines and uncover insights.
        </p>
      </div>

      <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg mb-6">
        <h3 className="text-lg font-medium mb-4 flex items-center dark:text-white">
          <Filter className="mr-2 h-4 w-4" /> Filter Medicines
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Input
            placeholder="Search name or INN"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="md:col-span-1"
          />
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((cat) => (
                <SelectItem key={cat} value={cat}>
                  {cat}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {statuses.map((st) => (
                <SelectItem key={st} value={st}>
                  {st}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={opinionStatusFilter} onValueChange={setOpinionStatusFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Opinion status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Opinions</SelectItem>
              {opinionStatuses.map((os) => (
                <SelectItem key={os} value={os}>
                  {os}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={therapeuticAreaFilter} onValueChange={setTherapeuticAreaFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Therapeutic area" />
            </SelectTrigger>
            <SelectContent className="max-h-60 overflow-y-auto">
              <SelectItem value="all">All Areas</SelectItem>
              {therapeuticAreas.map((ta) => (
                <SelectItem key={ta} value={String(ta)}>
                  {ta}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={pharmGroupFilter} onValueChange={setPharmGroupFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Pharmacotherapeutic group" />
            </SelectTrigger>
            <SelectContent className="max-h-60 overflow-y-auto">
              <SelectItem value="all">All Groups</SelectItem>
              {pharmGroups.map((pg) => (
                <SelectItem key={pg} value={String(pg)}>
                  {pg}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={acceleratedFilter} onValueChange={setAcceleratedFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Accelerated assessment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Accelerated?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={advancedTherapyFilter} onValueChange={setAdvancedTherapyFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Advanced therapy" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Advanced?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={biosimilarFilter} onValueChange={setBiosimilarFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Biosimilar" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Biosimilar?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={conditionalFilter} onValueChange={setConditionalFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Conditional approval" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Conditional?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={exceptionalFilter} onValueChange={setExceptionalFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Exceptional circumstances" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Exceptional?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={genericFilter} onValueChange={setGenericFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Generic or hybrid" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Generic/Hybrid?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={orphanFilter} onValueChange={setOrphanFilter}>
            <SelectTrigger>
              <SelectValue placeholder="Orphan medicine" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Orphan?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={primeFilter} onValueChange={setPrimeFilter}>
            <SelectTrigger>
              <SelectValue placeholder="PRIME" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">PRIME?</SelectItem>
              {yesNoOptions.map((val) => (
                <SelectItem key={val} value={val}>
                  {val}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="w-full h-64 mb-6">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={statusChartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" className="text-xs" />
            <YAxis allowDecimals={false} />
            <ReTooltip />
            <Bar dataKey="value" fill="#fb923c" />
          </BarChart>
        </ResponsiveContainer>
      </div>

      {favoriteMedicines.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Favorite Medicines</h2>
          <div className="grid md:grid-cols-2 gap-4">
            {favoriteMedicines.map((med) => (
              <Card key={med["Name of medicine"]}>
                <CardHeader className="flex justify-between items-start py-2">
                  <CardTitle className="text-lg">
                    {med["Name of medicine"]}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2"
                    onClick={() => toggleFavorite(med["Name of medicine"])}
                  >
                    <BookmarkCheck className="h-4 w-4 text-amber-500" />
                  </Button>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    {med["Medicine status"]}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {compareMedicines.length > 1 && (
        <div className="mb-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison</h2>
            <Button variant="outline" size="sm" onClick={() => setCompareNames([])}>
              Clear
            </Button>
          </div>
          <table className="min-w-full text-sm border rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border px-3 py-2 w-32 text-left">Field</th>
                {compareMedicines.map((m) => (
                  <th
                    key={m["Name of medicine"]}
                    className="border px-3 py-2 w-60 text-left"
                  >
                    {m["Name of medicine"]}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Status</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60">
                    {m["Medicine status"]}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">INN</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60">
                    {m["International non-proprietary name (INN) / common name"] || "-"}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Active substance</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60">
                    {m["Active substance"] || "-"}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border px-3 py-2 font-medium w-32">Indication</td>
                {compareMedicines.map((m) => (
                  <td key={m["Name of medicine"]} className="border px-3 py-2 w-60">
                    {m["Therapeutic indication"] || "-"}
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
        </div>
      )}

      {filteredMedicines.length === 0 ? (
        <div className="text-center py-12 text-muted-foreground">
          No medicines match your search.
        </div>
      ) : (
        <Accordion
          type="multiple"
          className="w-full"
          onValueChange={(v) => setOpenItems(v as string[])}
        >
          {filteredMedicines.slice(0, 100).map((med, idx) => {
            const value = `${med["Name of medicine"]}-${idx}`;
            return (
              <MedicineItem
                key={value}
                value={value}
                med={med}
                isOpen={openItems.includes(value)}
              />
            );
          })}
        </Accordion>
      )}
    </div>
  );
}
