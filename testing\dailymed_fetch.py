import requests
import json
from typing import Dict, List, Optional
import time

class OpenFDADrugAPI:
    """
    A reliable class to fetch drug label information from the FDA's OpenFDA API.
    This API is more stable than DailyMed and provides comprehensive drug information.
    """
    
    def __init__(self):
        self.base_url = "https://api.fda.gov/drug/label.json"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'OpenFDA-Python-Client/1.0'
        })
    
    def search_drug_by_brand_name(self, brand_name: str, limit: int = 5) -> Dict:
        """
        Search for drugs by brand name using OpenFDA API.
        
        Args:
            brand_name (str): The brand name of the drug to search for
            limit (int): Maximum number of results to return (default: 5)
        
        Returns:
            Dict: API response containing drug information
        """
        params = {
            'search': f'openfda.brand_name:"{brand_name}"',
            'limit': limit
        }
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"Error searching for drug: {e}")
            return {}
    
    def search_drug_by_generic_name(self, generic_name: str, limit: int = 5) -> Dict:
        """
        Search for drugs by generic name using OpenFDA API.
        
        Args:
            generic_name (str): The generic name of the drug to search for
            limit (int): Maximum number of results to return (default: 5)
        
        Returns:
            Dict: API response containing drug information
        """
        params = {
            'search': f'openfda.generic_name:"{generic_name}"',
            'limit': limit
        }
        
        try:
            response = self.session.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            return response.json()
        
        except requests.exceptions.RequestException as e:
            print(f"Error searching for drug by generic name: {e}")
            return {}
    
    def search_drug_flexible(self, drug_name: str, limit: int = 5) -> Dict:
        """
        Flexible search that tries both brand name and generic name.
        
        Args:
            drug_name (str): The drug name to search for
            limit (int): Maximum number of results to return
            
        Returns:
            Dict: Combined results from both search methods
        """
        print(f"Searching for '{drug_name}' by brand name...")
        brand_results = self.search_drug_by_brand_name(drug_name, limit)
        
        if not brand_results.get('results'):
            print(f"No brand name results, trying generic name search...")
            generic_results = self.search_drug_by_generic_name(drug_name, limit)
            return generic_results
        
        return brand_results
    
    def extract_drug_info(self, api_response: Dict) -> List[Dict]:
        """
        Extract and format key drug information from OpenFDA response.
        
        Args:
            api_response (Dict): Raw response from OpenFDA API
            
        Returns:
            List[Dict]: List of formatted drug information
        """
        if not api_response or 'results' not in api_response:
            return []
        
        formatted_drugs = []
        
        for result in api_response['results']:
            openfda = result.get('openfda', {})
            
            drug_info = {
                # Basic identification
                'brand_names': openfda.get('brand_name', []),
                'generic_names': openfda.get('generic_name', []),
                'manufacturer_name': openfda.get('manufacturer_name', []),
                'labeler_name': openfda.get('labeler_name', []),
                
                # Product details
                'product_ndc': openfda.get('product_ndc', []),
                'product_type': openfda.get('product_type', []),
                'route': openfda.get('route', []),
                'substance_name': openfda.get('substance_name', []),
                
                # Regulatory information
                'application_number': openfda.get('application_number', []),
                'nui': openfda.get('nui', []),
                'package_ndc': openfda.get('package_ndc', []),
                
                # Label sections
                'indications_and_usage': result.get('indications_and_usage', []),
                'contraindications': result.get('contraindications', []),
                'warnings': result.get('warnings', []),
                'warnings_and_cautions': result.get('warnings_and_cautions', []),
                'adverse_reactions': result.get('adverse_reactions', []),
                'drug_interactions': result.get('drug_interactions', []),
                'dosage_and_administration': result.get('dosage_and_administration', []),
                'overdosage': result.get('overdosage', []),
                'description': result.get('description', []),
                'clinical_pharmacology': result.get('clinical_pharmacology', []),
                'how_supplied': result.get('how_supplied', []),
                'storage_and_handling': result.get('storage_and_handling', []),
                
                # Patient information
                'patient_medication_information': result.get('patient_medication_information', []),
                'information_for_patients': result.get('information_for_patients', []),
                
                # Pregnancy and nursing
                'pregnancy': result.get('pregnancy', []),
                'nursing_mothers': result.get('nursing_mothers', []),
                'pediatric_use': result.get('pediatric_use', []),
                'geriatric_use': result.get('geriatric_use', []),
                
                # Other sections
                'active_ingredient': result.get('active_ingredient', []),
                'inactive_ingredient': result.get('inactive_ingredient', []),
                'purpose': result.get('purpose', []),
                'keep_out_of_reach_of_children': result.get('keep_out_of_reach_of_children', []),
                'questions': result.get('questions', []),
                
                # Metadata
                'set_id': result.get('set_id', ''),
                'id': result.get('id', ''),
                'effective_time': result.get('effective_time', ''),
                'version': result.get('version', '')
            }
            
            formatted_drugs.append(drug_info)
        
        return formatted_drugs
    
    def get_comprehensive_drug_info(self, drug_name: str, limit: int = 5) -> Dict:
        """
        Get comprehensive drug information with formatted output.
        
        Args:
            drug_name (str): Name of the drug to search for
            limit (int): Maximum number of results
            
        Returns:
            Dict: Comprehensive drug information
        """
        # Search for the drug
        raw_response = self.search_drug_flexible(drug_name, limit)
        
        if not raw_response.get('results'):
            return {'error': f'No results found for {drug_name}'}
        
        # Extract and format information
        formatted_drugs = self.extract_drug_info(raw_response)
        
        return {
            'search_term': drug_name,
            'total_results': len(formatted_drugs),
            'drugs': formatted_drugs,
            'meta': raw_response.get('meta', {})
        }

def display_drug_summary(drug_info: Dict, index: int = 0):
    """
    Display a formatted summary of drug information.
    
    Args:
        drug_info (Dict): Drug information from get_comprehensive_drug_info
        index (int): Index of the drug to display (default: 0 for first result)
    """
    if 'error' in drug_info:
        print(f"Error: {drug_info['error']}")
        return
    
    if not drug_info.get('drugs') or index >= len(drug_info['drugs']):
        print("No drug information available at the specified index.")
        return
    
    drug = drug_info['drugs'][index]
    
    print(f"\n{'='*60}")
    print(f"DRUG INFORMATION SUMMARY")
    print(f"{'='*60}")
    
    # Basic Information
    print(f"Brand Name(s): {', '.join(drug['brand_names']) if drug['brand_names'] else 'N/A'}")
    print(f"Generic Name(s): {', '.join(drug['generic_names']) if drug['generic_names'] else 'N/A'}")
    print(f"Manufacturer: {', '.join(drug['manufacturer_name']) if drug['manufacturer_name'] else 'N/A'}")
    print(f"Route(s): {', '.join(drug['route']) if drug['route'] else 'N/A'}")
    
    # Active ingredients
    if drug['active_ingredient']:
        print(f"\nActive Ingredient(s):")
        for ingredient in drug['active_ingredient'][:3]:  # Show first 3
            print(f"  • {ingredient}")
    
    # Purpose
    if drug['purpose']:
        print(f"\nPurpose:")
        for purpose in drug['purpose'][:2]:  # Show first 2
            print(f"  • {purpose}")
    
    # Indications
    if drug['indications_and_usage']:
        print(f"\nIndications and Usage:")
        indication_text = drug['indications_and_usage'][0]
        print(f"  {indication_text[:300]}{'...' if len(indication_text) > 300 else ''}")
    
    # Warnings
    if drug['warnings']:
        print(f"\nWarnings:")
        warning_text = drug['warnings'][0]
        print(f"  {warning_text[:300]}{'...' if len(warning_text) > 300 else ''}")
    
    # Dosage
    if drug['dosage_and_administration']:
        print(f"\nDosage and Administration:")
        dosage_text = drug['dosage_and_administration'][0]
        print(f"  {dosage_text[:300]}{'...' if len(dosage_text) > 300 else ''}")
    
    print(f"\n{'='*60}")

def save_drug_info_to_json(drug_info: Dict, filename: str):
    """
    Save comprehensive drug information to a JSON file.
    
    Args:
        drug_info (Dict): Drug information from get_comprehensive_drug_info
        filename (str): Output filename
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(drug_info, f, indent=2, ensure_ascii=False)
        print(f"Drug information saved to {filename}")
    except Exception as e:
        print(f"Error saving to file: {e}")

def main():
    """
    Example usage of the OpenFDA Drug API client.
    """
    # Initialize the API client
    api = OpenFDADrugAPI()
    
    # Example drugs to search for
    test_drugs = ["Tylenol", "Advil", "Lipitor"]
    
    for drug_name in test_drugs:
        print(f"\n🔍 Searching for: {drug_name}")
        print("-" * 60)
        
        # Get comprehensive drug information
        drug_info = api.get_comprehensive_drug_info(drug_name, limit=3)
        
        if 'error' not in drug_info:
            print(f"Found {drug_info['total_results']} result(s)")
            
            # Display summary of the first result
            display_drug_summary(drug_info, index=0)
            
            # Save to file
            filename = f"{drug_name.lower()}_drug_info.json"
            save_drug_info_to_json(drug_info, filename)
        else:
            print(drug_info['error'])
        
        time.sleep(1)  # Be respectful to the API

if __name__ == "__main__":
    main()