import { NextRequest, NextResponse } from 'next/server';
import { XMLParser } from 'fast-xml-parser';

export async function GET(request: NextRequest) {
  try {
    const params = request.nextUrl.searchParams;
    const query = params.get('query');
    if (!query) {
      return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 });
    }

    const page = parseInt(params.get('page') || '1', 10);
    const pageSize = parseInt(params.get('pageSize') || '20', 10);
    const startYear = params.get('startYear');
    const endYear = params.get('endYear');
    const sort = params.get('sort') || 'relevance';
    const author = params.get('author');
    const journal = params.get('journal');

    const retstart = (page - 1) * pageSize;

    let term = encodeURIComponent(query);
    if (startYear || endYear) {
      const range = `${startYear || '1900'}:${endYear || new Date().getFullYear()}`;
      term += `%20AND%20(${range}[dp])`;
    }
    if (author) {
      term += `%20AND%20${encodeURIComponent(author)}[Author]`;
    }
    if (journal) {
      term += `%20AND%20${encodeURIComponent(journal)}[Journal]`;
    }

    const baseParams = 'tool=vendorhub&email=<EMAIL>';
    const esearchUrl = `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi?db=pubmed&retmode=json&retmax=${pageSize}&retstart=${retstart}&sort=${sort === 'date' ? 'pub+date' : 'relevance'}&term=${term}&${baseParams}`;
    const esearchRes = await fetch(esearchUrl, { cache: 'no-store' });
    if (!esearchRes.ok) throw new Error('ESearch request failed');
    const esearchJson = await esearchRes.json();
    const ids: string[] = esearchJson.esearchresult.idlist;

    if (!ids || ids.length === 0) {
      return NextResponse.json({ count: 0, results: [] }, { headers: { 'Cache-Control': 'no-store' } });
    }

    const esummaryUrl = `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esummary.fcgi?db=pubmed&retmode=json&id=${ids.join(',')}&${baseParams}`;
    const esummaryRes = await fetch(esummaryUrl, { cache: 'no-store' });
    if (!esummaryRes.ok) throw new Error('ESummary request failed');
    const summaryJson = await esummaryRes.json();
    const summaryResult = summaryJson.result;

    const efetchUrl = `https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id=${ids.join(',')}&retmode=xml&${baseParams}`;
    const efetchRes = await fetch(efetchUrl, { cache: 'no-store' });
    const xml = await efetchRes.text();
    const parser = new XMLParser({ ignoreAttributes: false });
    const fetchJson = parser.parse(xml);
    const articles = Array.isArray(fetchJson.PubmedArticleSet?.PubmedArticle)
      ? fetchJson.PubmedArticleSet.PubmedArticle
      : [fetchJson.PubmedArticleSet?.PubmedArticle].filter(Boolean);

    const abstracts: Record<string, string> = {};
    const pubTypes: Record<string, string[]> = {};
    const meshTerms: Record<string, string[]> = {};

    for (const art of articles) {
      const pmid = art?.MedlineCitation?.PMID?.['#text'] || art?.MedlineCitation?.PMID;

      let ab = art?.MedlineCitation?.Article?.Abstract?.AbstractText;
      if (Array.isArray(ab)) {
        ab = ab.map((a: any) => (typeof a === 'string' ? a : a['#text'])).join(' ');
      } else if (typeof ab === 'object' && ab?.['#text']) {
        ab = ab['#text'];
      }
      if (typeof ab === 'string') abstracts[pmid] = ab;

      const pt = art?.MedlineCitation?.Article?.PublicationTypeList?.PublicationType;
      if (Array.isArray(pt)) {
        pubTypes[pmid] = pt.map((p: any) => (typeof p === 'string' ? p : p['#text'])).filter(Boolean);
      } else if (pt) {
        pubTypes[pmid] = [typeof pt === 'string' ? pt : pt['#text']];
      }

      const mesh = art?.MedlineCitation?.MeshHeadingList?.MeshHeading;
      if (Array.isArray(mesh)) {
        meshTerms[pmid] = mesh
          .map((m: any) => {
            const d = m.DescriptorName;
            return typeof d === 'string' ? d : d?.['#text'];
          })
          .filter(Boolean);
      } else if (mesh?.DescriptorName) {
        const d = mesh.DescriptorName;
        meshTerms[pmid] = [typeof d === 'string' ? d : d['#text']];
      }
    }


    const results = ids.map((id) => {
      const item = summaryResult[id];
      return {
        id,
        title: item?.title,
        pubdate: item?.pubdate,
        journal: item?.fulljournalname,
        authors: Array.isArray(item?.authors) ? item.authors.map((a: any) => a.name).join(', ') : '',
        abstract: abstracts[id] || '',
        publicationTypes: pubTypes[id] || [],
        meshTerms: meshTerms[id] || [],
      };
    });

    return NextResponse.json(
      { count: parseInt(esearchJson.esearchresult.count), results },
      { headers: { 'Cache-Control': 'no-store' } }
    );
  } catch (err) {
    console.error('PubMed search error', err);
    return NextResponse.json(
      { error: 'Failed to search PubMed' },
      { status: 500, headers: { 'Cache-Control': 'no-store' } }
    );
  }
}
