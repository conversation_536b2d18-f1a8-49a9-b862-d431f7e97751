

"use client";

import React, { useState, useMemo, useEffect, useRef, Suspense } from "react";
import Link from "next/link";
import {
  ArrowLeft,
  Search,
  Save,
  Download,
  Clipboard,
  X,
  Bookmark,
  BookmarkCheck,
  Plus,
  Sparkles,
  Lightbulb,
  Loader2,
} from "lucide-react";
import axios from "axios";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SUBPAGES } from "@/lib/subpages";
import { useSearchParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ThemeToggle } from "@/components/ThemeToggle";
import { useLocalStorage } from "@/hooks/use-local-storage";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";

interface PubMedResult {
  id: string;
  title: string;
  pubdate?: string;
  journal?: string;
  authors?: string;
  abstract?: string;
  publicationTypes?: string[];
  meshTerms?: string[];
}

interface SavedQuery {
  query: string;
  startYear?: string;
  endYear?: string;
  sort: string;
  author?: string;
  journal?: string;
}

interface JournalDataItem {
  journal: string;
  value: number;
}

interface KeywordTreeDataItem {
  name: string;
  size: number;
  fill: string;
}

interface SearchItem {
  id: number;
  query: string;
  author: string;
  journal: string;
  startYear: string;
  endYear: string;
  sort: string;
  results: PubMedResult[];
  page: number;
  totalCount: number;
  isSearching: boolean;
  hasRun: boolean;
}

function PubMedExplorerContent() {
  // Add this to prevent hydration mismatches
  const [mounted, setMounted] = useState(false);
  
  // Search items state - allow up to five simultaneous searches
  const [searches, setSearches] = useState<SearchItem[]>([
    {
      id: Date.now(),
      query: "",
      author: "",
      journal: "",
      startYear: "",
      endYear: "",
      sort: "relevance",
      results: [],
      page: 1,
      totalCount: 0,
      isSearching: false,
      hasRun: false,
    },
  ]);
  const [savedQueries, setSavedQueries] = useLocalStorage<SavedQuery[]>(
    "pubmedSavedQueries",
    [],
  );
  const [bookmarks, setBookmarks] = useLocalStorage<PubMedResult[]>(
    "pubmedBookmarks",
    [],
  );
  const [expandedIds, setExpandedIds] = useState<string[]>([]);
  const [compareIds, setCompareIds] = useState<string[]>([]);
  const [expandedCompareIds, setExpandedCompareIds] = useState<string[]>([]);
  const [viewType, setViewType] = useState<"bar" | "line">("bar");
  const [keywordView, setKeywordView] = useState<"badges" | "treemap">("badges");
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [journalCounts, setJournalCounts] = useState<number[]>([5]);
  const [authorCounts, setAuthorCounts] = useState<number[]>([5]);
  const [sidePage, setSidePage] = useState("");
  const params = useSearchParams();
  const embedded = params.get("embedded") === "1";
  const [bookmarkSort, setBookmarkSort] = useState<'added' | 'title' | 'date'>('added');
  const sortedBookmarks = useMemo(() => {
    const b = [...bookmarks];
    if (bookmarkSort === 'title') {
      b.sort((a, b2) => a.title.localeCompare(b2.title));
    } else if (bookmarkSort === 'date') {
      b.sort((a, b2) => (b2.pubdate || '').localeCompare(a.pubdate || ''));
    }
    return b;
  }, [bookmarks, bookmarkSort]);
  const [hoverTooltip, setHoverTooltip] = useState<
    | { label: string; articles: PubMedResult[]; x: number; y: number }
    | null
  >(null);
  const [pendingSearch, setPendingSearch] = useState<
    | { idx: number; query: string }
    | null
  >(null);
  const hideTooltipTimeout = useRef<NodeJS.Timeout | null>(null);
  const { toast } = useToast();

  // --- ChatGPT enhanced features ---
  const [articleSummaries, setArticleSummaries] = useState<Record<string, string>>({});
  const [plainSummaries, setPlainSummaries] = useState<Record<string, string>>({});
  const [searchSummaries, setSearchSummaries] = useState<Record<number, string>>({});
  const [querySuggestions, setQuerySuggestions] = useState<Record<number, string[]>>({});
  const [researchQuestions, setResearchQuestions] = useState<Record<number, string[]>>({});
  const [bookmarkSummary, setBookmarkSummary] = useState<string>("");
  const [comparisonSummary, setComparisonSummary] = useState<string>("");
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const comparisonRef = useRef<HTMLDivElement | null>(null);

  const setLoad = (key: string, val: boolean) =>
    setLoading((prev) => ({ ...prev, [key]: val }));

  const fetchChat = async (prompt: string) => {
    const res = await fetch("/api/chat", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ messages: [{ role: "user", content: prompt }] }),
    });
    if (!res.ok) throw new Error("ChatGPT request failed");
    const data = await res.json();
    return data.content as string;
  };

  const parseBullets = (text: string) =>
    text
      .split(/\n+/)
      .map((l) => l.replace(/^[-•*\d.\s]+/, "").trim())
      .filter(Boolean);

  // Set mounted to true after initial render
  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (pendingSearch) {
      search(pendingSearch.idx, 1, pendingSearch.query);
      setPendingSearch(null);
    }
  }, [pendingSearch]);

  const highlightText = (text: string, q: string) => {
    const words = q
      .split(/\s+/)
      .map((w) => w.replace(/[^\w]/g, ""))
      .filter(Boolean);
    if (words.length === 0) return text;
    const pattern = new RegExp(`(${words.join("|")})`, "gi");
    return text.split(pattern).map((part, i) =>
      pattern.test(part) ? (
        <mark key={i} className="bg-amber-200 dark:bg-amber-700">
          {part}
        </mark>
      ) : (
        part
      ),
    );
  };

  const extractNctIds = (text: string) => {
    const matches = text.match(/NCT\d{8}/gi);
    return matches ? Array.from(new Set(matches.map((m) => m.toUpperCase()))) : [];
  };

  const showHoverTooltip = (
    label: string,
    articles: PubMedResult[],
    e: any,
  ) => {
    if (hideTooltipTimeout.current) {
      clearTimeout(hideTooltipTimeout.current);
      hideTooltipTimeout.current = null;
    }
    setHoverTooltip({ label, articles, x: e.clientX, y: e.clientY });
  };

  const scheduleHideTooltip = () => {
    hideTooltipTimeout.current = setTimeout(() => setHoverTooltip(null), 500);
  };

  const cancelHideTooltip = () => {
    if (hideTooltipTimeout.current) {
      clearTimeout(hideTooltipTimeout.current);
      hideTooltipTimeout.current = null;
    }
  };

  // ChatGPT feature helpers
  const summarizeArticle = async (item: PubMedResult) => {
    if (!item.abstract) return;
    setLoad(`article-${item.id}`, true);
    const prompt = `Summarize the following PubMed abstract in three short bullet points using the \u2022 symbol. Do not number the points.\n\n${item.abstract}`;
    try {
      const summary = await fetchChat(prompt);
      setArticleSummaries((prev) => ({ ...prev, [item.id]: summary }));
    } finally {
      setLoad(`article-${item.id}`, false);
    }
  };

  const plainLanguageSummary = async (item: PubMedResult) => {
    if (!item.abstract) return;
    setLoad(`plain-${item.id}`, true);
    const prompt = `Explain the following PubMed abstract in plain language using three \u2022 bullet points without numbers.\n\n${item.abstract}`;
    try {
      const summary = await fetchChat(prompt);
      setPlainSummaries((prev) => ({ ...prev, [item.id]: summary }));
    } finally {
      setLoad(`plain-${item.id}`, false);
    }
  };

  const summarizeSearchResults = async (idx: number) => {
    const articles = searches[idx]?.results || [];
    if (articles.length === 0) return;
    setLoad(`search-${idx}`, true);
    const content = articles
      .slice(0, 10)
      .map((a) => `Title: ${a.title}\n${a.abstract}`)
      .join("\n\n");
    const prompt = `Provide a high level summary of these PubMed articles in five \u2022 bullet points. Do not use numbers.\n\n${content}`;
    try {
      const summary = await fetchChat(prompt);
      setSearchSummaries((prev) => ({ ...prev, [idx]: summary }));
    } finally {
      setLoad(`search-${idx}`, false);
    }
  };

  const suggestQueries = async (idx: number) => {
    const q = searches[idx]?.query || "";
    if (!q.trim()) return;
    setLoad(`query-${idx}`, true);
    const prompt = `Suggest three PubMed search queries related to: ${q}. Present each suggestion on a new line with a \u2022 bullet.`;
    try {
      const resp = await fetchChat(prompt);
      setQuerySuggestions((prev) => ({ ...prev, [idx]: resp.split(/\n+/).filter(Boolean) }));
    } finally {
      setLoad(`query-${idx}`, false);
    }
  };

  const suggestResearchQuestions = async (idx: number) => {
    const articles = searches[idx]?.results || [];
    if (articles.length === 0) return;
    setLoad(`research-${idx}`, true);
    const text = articles.slice(0, 5).map((a) => a.title).join("; ");
    const prompt = `Based on these PubMed articles: ${text}, suggest three follow up research questions. Use \u2022 bullet points without numbers.`;
    try {
      const resp = await fetchChat(prompt);
      setResearchQuestions((prev) => ({ ...prev, [idx]: resp.split(/\n+/).filter(Boolean) }));
    } finally {
      setLoad(`research-${idx}`, false);
    }
  };

  const summarizeBookmarks = async () => {
    if (bookmarks.length === 0) return;
    setLoad("bookmark", true);
    const text = bookmarks
      .slice(0, 10)
      .map((b) => `Title: ${b.title}\n${b.abstract}`)
      .join("\n\n");
    const prompt = `Summarize these bookmarked PubMed articles in five \u2022 bullet points without numbers.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setBookmarkSummary(resp);
    } finally {
      setLoad("bookmark", false);
    }
  };

  const summarizeComparison = async () => {
    if (compareItems.length < 2) return;
    setLoad("comparison", true);
    const text = compareItems
      .slice(0, 5)
      .map((c) => `Title: ${c.title}\n${c.abstract}`)
      .join("\n\n");
    const prompt = `Compare these PubMed articles and summarize their key similarities and differences in up to five \u2022 bullet points without numbers.\n\n${text}`;
    try {
      const resp = await fetchChat(prompt);
      setComparisonSummary(resp);
    } finally {
      setLoad("comparison", false);
    }
  };

  const renderAuthorTick = ({ x, y, payload }: any) => (
    <text x={x} y={y} textAnchor="end" dominantBaseline="middle" className="whitespace-nowrap">
      {payload.value}
    </text>
  );

  const allResults = useMemo(() => searches.flatMap((s) => s.results), [searches]);

  // Chart data will be calculated individually for each search

  const getYearData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      const match = r.pubdate?.match(/\d{4}/);
      if (match) {
        counts[match[0]] = (counts[match[0]] || 0) + 1;
      }
    });
    return Object.entries(counts)
      .map(([year, count]) => ({ year, count }))
      .sort((a, b) => parseInt(a.year) - parseInt(b.year));
  };

  const getJournalData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.journal) counts[r.journal] = (counts[r.journal] || 0) + 1;
    });
    return Object.entries(counts)
      .map(([journal, value]) => ({ journal, value }))
      .sort((a, b) => b.value - a.value);
  };

  const getAuthorData = (results: PubMedResult[]) => {
    const counts: Record<string, number> = {};
    results.forEach((r) => {
      if (r.authors) {
        r.authors.split(",").forEach((a) => {
          const name = a.trim();
          if (name) counts[name] = (counts[name] || 0) + 1;
        });
      }
    });
    return Object.entries(counts)
      .map(([author, value]) => ({ author, value }))
      .sort((a, b) => b.value - a.value);
  };

  const getArticlesByYear = (results: PubMedResult[], year: string) =>
    results.filter((r) => r.pubdate && r.pubdate.includes(year));

  const getArticlesByJournal = (results: PubMedResult[], journal: string) =>
    results.filter((r) => r.journal === journal);

  const getArticlesByAuthor = (results: PubMedResult[], author: string) =>
    results.filter(
      (r) =>
        r.authors &&
        r.authors
          .split(",")
          .map((a) => a.trim())
          .includes(author),
    );

  const compareItems = useMemo(
    () => {
      if (!mounted) return [];
      return allResults.filter((r) => compareIds.includes(r.id));
    },
    [allResults, compareIds, mounted]
  );

  const saveCurrentQuery = (idx: number) => {
    const s = searches[idx];
    if (!s.query.trim()) {
      toast({
        title: "Search query required",
        description: "Enter a query before saving",
        variant: "destructive",
      });
      return;
    }
    const newQuery: SavedQuery = {
      query: s.query,
      startYear: s.startYear || undefined,
      endYear: s.endYear || undefined,
      sort: s.sort,
      author: s.author || undefined,
      journal: s.journal || undefined,
    };
    const exists = savedQueries.some(
      (q) =>
        q.query === newQuery.query &&
        (q.startYear || "") === (newQuery.startYear || "") &&
        (q.endYear || "") === (newQuery.endYear || "") &&
        q.sort === newQuery.sort &&
        (q.author || "") === (newQuery.author || "") &&
        (q.journal || "") === (newQuery.journal || ""),
    );
    if (exists) return;
    setSavedQueries([...savedQueries, newQuery]);
    toast({ title: "Search saved" });
  };

  const loadSavedQuery = (sq: SavedQuery) => {
    const emptyIndex = searches.findIndex((s) => !s.query.trim());
    
    if (emptyIndex !== -1) {
      // For existing empty search slot, update and then search
      const updatedSearches = [...searches];
      updatedSearches[emptyIndex] = { 
        ...updatedSearches[emptyIndex], 
        query: sq.query,
        author: sq.author || "",
        journal: sq.journal || "",
        startYear: sq.startYear || "",
        endYear: sq.endYear || "",
        sort: sq.sort || "relevance",
        page: 1,
        isSearching: true
      };
      
      // Update state with the modified array
      setSearches(updatedSearches);
      
      // Use the updated object directly for search
      setTimeout(() => {
        search(emptyIndex, 1, sq.query);
      }, 50);
    } else if (searches.length < 3) {
      // For new search, create the search object first
      const newSearch = {
        id: Date.now(),
        query: sq.query,
        author: sq.author || "",
        journal: sq.journal || "",
        startYear: sq.startYear || "",
        endYear: sq.endYear || "",
        sort: sq.sort || "relevance",
        results: [],
        page: 1,
        totalCount: 0,
        isSearching: true,
        hasRun: false,
      };
      
      // Calculate the new index
      const newSearchIndex = searches.length;
      
      // Update state with the new search added
      setSearches(prev => [...prev, newSearch]);
      setJournalCounts(prev => [...prev, 5]);
      setAuthorCounts(prev => [...prev, 5]);
      
      // Create a pendingSearch object instead of directly calling search
      setPendingSearch({
        idx: newSearchIndex,
        query: sq.query
      });
    } else {
      // If already at 3 searches, show a toast notification
      toast({
        title: "Maximum searches reached",
        description: "Please remove a search before adding a new one",
        variant: "destructive",
      });
    }
  };

  const removeSavedQuery = (index: number) => {
    setSavedQueries(savedQueries.filter((_, i) => i !== index));
  };

  const exportCsv = () => {
    if (allResults.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = allResults.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportSearchCsv = (idx: number) => {
    const results = searches[idx]?.results || [];
    if (results.length === 0) return;
    const header = [
      "PMID",
      "Title",
      "Publication Date",
      "Journal",
      "Authors",
      "Abstract",
    ];
    const rows = results.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-results-${idx + 1}-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportComparisonCsv = () => {
    if (compareItems.length === 0) return;
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = compareItems.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-comparison-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const copyCitation = (item: PubMedResult, id: string) => {
    const citation = `${item.authors ? item.authors + ". " : ""}${item.title}. ${
      item.journal ? item.journal + ". " : ""
    }${item.pubdate ? item.pubdate + ". " : ""}PMID: ${item.id}.`;
    navigator.clipboard.writeText(citation).then(() => {
      setCopiedId(id);
      toast({ title: "Citation copied" });
      setTimeout(() => setCopiedId(null), 2000);
    });
  };

  const toggleBookmark = (item: PubMedResult) => {
    const exists = bookmarks.find((p) => p.id === item.id);
    if (exists) {
      setBookmarks(bookmarks.filter((p) => p.id !== item.id));
    } else {
      setBookmarks([...bookmarks, item]);
    }
  };

  const toggleCompare = (id: string) => {
    if (compareIds.includes(id)) {
      setCompareIds(compareIds.filter((c) => c !== id));
    } else {
      setCompareIds([...compareIds, id]);
    }
  };

  const removeSearch = (id: number) => {
    setSearches((prev) => {
      const idx = prev.findIndex((s) => s.id === id);
      if (idx === -1) return prev;
      setJournalCounts((c) => c.filter((_, i) => i !== idx));
      setAuthorCounts((c) => c.filter((_, i) => i !== idx));
      return prev.filter((s) => s.id !== id);
    });
  };

  const toggleAbstract = (id: string) => {
    if (expandedIds.includes(id)) {
      setExpandedIds(expandedIds.filter((i) => i !== id));
    } else {
      setExpandedIds([...expandedIds, id]);
    }
  };

  const toggleCompareAbstract = (id: string) => {
    if (expandedCompareIds.includes(id)) {
      setExpandedCompareIds(expandedCompareIds.filter((i) => i !== id));
    } else {
      setExpandedCompareIds([...expandedCompareIds, id]);
    }
  };

  const clearFilters = (idx: number) => {
    setSearches((prev) =>
      prev.map((s, i) =>
        i === idx
          ? { ...s, author: "", journal: "", startYear: "", endYear: "", sort: "relevance" }
          : s,
      ),
    );
  };

  const clearComparison = () => {
    setCompareIds([]);
  };

  const scrollToComparison = () => {
    if (comparisonRef.current) {
      comparisonRef.current.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const clearBookmarks = () => {
    setBookmarks([]);
  };

  const exportBookmarksCsv = () => {
    if (bookmarks.length === 0) return;
    const header = ["PMID", "Title", "Publication Date", "Journal", "Authors", "Abstract"];
    const rows = bookmarks.map((r) =>
      [
        r.id,
        r.title.replace(/\"/g, '""'),
        r.pubdate || "",
        r.journal || "",
        r.authors ? r.authors.replace(/\"/g, '""') : "",
        r.abstract ? r.abstract.replace(/\"/g, '""') : "",
      ]
        .map((v) => `"${v}"`)
        .join(","),
    );
    const csvContent = [header.join(","), ...rows].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `pubmed-bookmarks-${Date.now()}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const resetAll = () => {
    setSearches([
      {
        id: Date.now(),
        query: "",
        author: "",
        journal: "",
        startYear: "",
        endYear: "",
        sort: "relevance",
        results: [],
        page: 1,
        totalCount: 0,
        isSearching: false,
        hasRun: false,
      },
    ]);
    setJournalCounts([5]);
    setAuthorCounts([5]);
    setCompareIds([]);
    setExpandedIds([]);
    setExpandedCompareIds([]);
  };
  const search = async (idx: number, pageOverride?: number, queryOverride?: string) => {
    const item = searches[idx];
    if (!item) {
      console.error(`Search item at index ${idx} is undefined`);
      return;
    }
    
    const q = queryOverride ?? item.query;
    if (!q.trim()) return;
    
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, isSearching: true } : s)));
    
    try {
      const response = await axios.get("/api/pubmed-search", {
        params: {
          query: q,
          page: pageOverride || item.page,
          pageSize: 20,
          startYear: item.startYear || undefined,
          endYear: item.endYear || undefined,
          sort: item.sort || "relevance",
          author: item.author || undefined,
          journal: item.journal || undefined,
        },
      });
      
      setSearches((prev) =>
        prev.map((s, i) => {
          if (i !== idx) return s;
          const newResults = pageOverride && pageOverride > 1 ? [...s.results, ...response.data.results] : response.data.results;
          return {
            ...s,
            results: newResults,
            totalCount: response.data.count,
            page: pageOverride || s.page,
            isSearching: false,
            hasRun: true,
          };
        }),
      );
    } catch (error) {
      console.error("PubMed search error", error);
      setSearches((prev) =>
        prev.map((s, i) =>
          i === idx ? { ...s, results: pageOverride && pageOverride > 1 ? s.results : [], isSearching: false, hasRun: true } : s,
        ),
      );
    }
  };

  const handleSubmit = (e: React.FormEvent, idx: number) => {
    e.preventDefault();
    setSearches((prev) => prev.map((s, i) => (i === idx ? { ...s, page: 1 } : s)));
    search(idx, 1);
  };

  const loadMore = (idx: number) => {
    const nextPage = searches[idx].page + 1;
    search(idx, nextPage);
  };

  const displayedCount = searches.filter((s) => s.results.length > 0).length;
  const gridColumns =
    displayedCount === 1
      ? "grid grid-cols-1 gap-8"
      : displayedCount === 2
        ? "grid grid-cols-1 md:grid-cols-2 gap-8"
        : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8";

  // Only render the full component when mounted
  if (!mounted) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-between items-center mb-6">
          {embedded ? (
            <Button
              variant="ghost"
              disabled
              className="pl-0 opacity-50 cursor-not-allowed"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Evicenter
            </Button>
          ) : (
            <Link href="/">
              <Button variant="ghost" className="pl-0 hover:bg-transparent">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Evicenter
              </Button>
            </Link>
          )}
          <div className="flex items-center gap-2">
            <Select value={sidePage} onValueChange={setSidePage}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select page" />
              </SelectTrigger>
              <SelectContent>
                {SUBPAGES.filter((p) => p.value !== "pubmed-explorer").map(
                  (p) => (
                    <SelectItem key={p.value} value={p.value}>
                      {p.label}
                    </SelectItem>
                  ),
                )}
              </SelectContent>
            </Select>
            <Link href={`/side-by-side?left=pubmed-explorer&right=${sidePage}`}>
              <Button variant="secondary" disabled={!sidePage}>
                Open
              </Button>
            </Link>
            <ThemeToggle />
          </div>
        </div>
        <h1 className="text-3xl font-bold mb-6">PubMed Explorer</h1>
        <div className="prose max-w-none mb-8 dark:text-gray-200">
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        {embedded ? (
          <Button
            variant="ghost"
            disabled
            className="pl-0 opacity-50 cursor-not-allowed"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Evicenter
          </Button>
        ) : (
          <Link href="/">
            <Button variant="ghost" className="pl-0 hover:bg-transparent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Evicenter
            </Button>
          </Link>
        )}
        <div className="flex items-center gap-2">
          <Select value={sidePage} onValueChange={setSidePage}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select page" />
            </SelectTrigger>
            <SelectContent>
              {SUBPAGES.filter((p) => p.value !== "pubmed-explorer").map((p) => (
                <SelectItem key={p.value} value={p.value}>
                  {p.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Link href={`/side-by-side?left=pubmed-explorer&right=${sidePage}`}>
            <Button variant="secondary" disabled={!sidePage}>
              Open
            </Button>
          </Link>
          <ThemeToggle />
        </div>
      </div>

      <h1 className="text-3xl font-bold mb-6">PubMed Explorer</h1>

      <div className="prose max-w-none mb-8 dark:text-gray-200">
        <p>Search the PubMed database like never before.</p>
      </div>

      <div className="space-y-4 mb-8">
        {searches.map((s, idx) => (
          <form
            key={s.id}
            onSubmit={(e) => handleSubmit(e, idx)}
            className="flex flex-wrap gap-2 items-end"
          >
            <Input
              type="search"
              placeholder="Enter keywords..."
              value={s.query}
              onChange={(e) =>
                setSearches((prev) =>
                  prev.map((p, i) => (i === idx ? { ...p, query: e.target.value } : p)),
                )
              }
              className="flex-1 min-w-[200px]"
            />
            <Input
              type="number"
              placeholder="Start Year"
              value={s.startYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, startYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="number"
              placeholder="End Year"
              value={s.endYear}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, endYear: e.target.value } : p)))
              }
              className="w-32"
            />
            <Input
              type="text"
              placeholder="Author"
              value={s.author}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, author: e.target.value } : p)))
              }
              className="w-40"
            />
            <Input
              type="text"
              placeholder="Journal"
              value={s.journal}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, journal: e.target.value } : p)))
              }
              className="w-40"
            />
            <select
              value={s.sort}
              onChange={(e) =>
                setSearches((prev) => prev.map((p, i) => (i === idx ? { ...p, sort: e.target.value } : p)))
              }
              className="border rounded-md px-2 py-2 text-sm h-10 bg-background text-foreground"
            >
              <option value="relevance">Relevance</option>
              <option value="date">Most Recent</option>
            </select>
            <select
              value={viewType}
              onChange={(e) => setViewType(e.target.value as "bar" | "line")}
              className="border rounded-md px-2 py-2 text-sm h-10 bg-background text-foreground"
            >
              <option value="bar">Bar Chart</option>
              <option value="line">Line Chart</option>
            </select>
            <Button
              type="submit"
              className="bg-amber-400 text-black hover:bg-amber-500"
              disabled={s.isSearching}
            >
              {s.isSearching ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black" />
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" /> Search
                </>
              )}
            </Button>
            <Button
              type="button"
              onClick={() => saveCurrentQuery(idx)}
              variant="outline"
              className="flex items-center gap-1"
            >
              <Save className="h-4 w-4" /> Save
            </Button>
            <Button type="button" onClick={() => clearFilters(idx)} variant="outline">
              Clear Filters
            </Button>
            {searches.length > 1 && (
              <Button type="button" variant="ghost" onClick={() => removeSearch(s.id)}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </form>
        ))}
        <div className="flex items-center gap-2">
          {searches.length < 3 && (
            <Button
              variant="outline"
              onClick={() => {
                setSearches((prev) => [
                  ...prev,
                  {
                    id: Date.now(),
                    query: "",
                    author: "",
                    journal: "",
                    startYear: "",
                    endYear: "",
                    sort: "relevance",
                    results: [],
                    page: 1,
                    totalCount: 0,
                    isSearching: false,
                    hasRun: false,
                  },
                ]);
                setJournalCounts((prev) => [...prev, 5]);
                setAuthorCounts((prev) => [...prev, 5]);
              }}
              className="h-10"
            >
              <Plus className="h-4 w-4 mr-2" /> Add Search
            </Button>
          )}
          <Button variant="outline" onClick={resetAll} className="h-10">
            Reset All
          </Button>
        </div>
      </div>

      {savedQueries.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Saved Searches</h2>
          <div className="flex flex-wrap gap-2">
            {savedQueries.map((sq, idx) => (
              <div key={idx} className="flex items-center gap-1 border rounded px-2 py-0">
                {/* adjust py value above to change saved search box height */}
                <Button
                  variant="link"
                  className="p-0 text-sm"
                  onClick={() => loadSavedQuery(sq)}
                >
                  {sq.query}
                </Button>
                <button
                  onClick={() => removeSavedQuery(idx)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {bookmarks.length > 0 && (
        <div className="mb-8">
          <h2 className="text-xl font-bold mb-2">Bookmarked Articles</h2>
          <div className="flex flex-wrap gap-2 mb-2">
            <div className="flex items-center gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeBookmarks}
                disabled={loading.bookmark}
                className="gap-1"
              >
                {loading.bookmark ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize Bookmarks
                  </>
                )}
              </Button>
            </div>
          </div>
          {bookmarkSummary && (
            <div className="mb-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button 
                onClick={() => setBookmarkSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close bookmark summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Bookmark Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(bookmarkSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
          <div className="space-y-2">
            {sortedBookmarks.map((b, idx) => (
              <div key={idx} className="flex items-center gap-2">
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${b.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                  title={`${b.authors?.split(",")[0]?.trim() || ""}${
                    b.journal ? ` - ${b.journal}` : ""
                  }${
                    b.pubdate ? ` (${b.pubdate.match(/\d{4}/)?.[0] || b.pubdate})` : ""
                  }`}
                >
                  {b.title}
                </a>
                <button
                  onClick={() => toggleBookmark(b)}
                  className="text-muted-foreground hover:text-red-500"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}



      {/* {allResults.length > 0 && (
        <div className="mb-8 space-y-4">
          <div className="flex justify-between">
            <Button variant="outline" onClick={exportCsv} className="gap-1">
              <Download className="h-4 w-4" /> Export CSV
            </Button>
          </div>
        </div>
      )} */}

      {compareItems.length > 1 && (
        <div ref={comparisonRef} className="mb-8 overflow-auto space-y-2">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold">Comparison</h2>
            <div className="flex gap-2 items-center">
              <Button variant="outline" size="sm" onClick={exportComparisonCsv} className="gap-1">
                <Download className="h-4 w-4" /> Export
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={summarizeComparison}
                disabled={loading.comparison}
                className="gap-1"
              >
                {loading.comparison ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" /> Loading...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4" /> Summarize
                  </>
                )}
              </Button>
              <Button variant="outline" size="sm" onClick={clearComparison}>
                Clear
              </Button>
            </div>
          </div>
          <table className="min-w-full text-sm border-2 border-gray-300 dark:border-gray-600 rounded-md overflow-hidden">
            <thead className="bg-muted">
              <tr>
                <th className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-32 text-left">Field</th>
                {compareItems.map((c) => (
                  <th key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 text-left">
                    {c.title}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-border">
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Journal</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.journal}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Pub Date</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.pubdate}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">PMID</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">
                    <a
                      href={`https://pubmed.ncbi.nlm.nih.gov/${c.id}/`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-amber-600 underline"
                    >
                      {c.id}
                    </a>
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Authors</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">{c.authors}</td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Abstract</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60">
                    {c.abstract && c.abstract.length > 250 && !expandedCompareIds.includes(c.id) ? (
                      <>
                        {c.abstract.substring(0, 250)}...
                        <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                          Show More
                        </Button>
                      </>
                    ) : (
                      <>
                        {c.abstract}
                        {c.abstract && c.abstract.length > 250 && (
                          <Button variant="link" size="sm" className="h-4 px-1" onClick={() => toggleCompareAbstract(c.id)}>
                            Show Less
                          </Button>
                        )}
                      </>
                    )}
                  </td>
                ))}
              </tr>
              <tr>
                <td className="border border-gray-300 dark:border-gray-600 px-3 py-2 font-medium w-32">Actions</td>
                {compareItems.map((c) => (
                  <td key={c.id} className="border border-gray-300 dark:border-gray-600 px-3 py-2 w-60 space-x-1">
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => copyCitation(c, c.id)}>
                      {copiedId === c.id ? <span className="text-xs text-green-600">Copied</span> : <Clipboard className="h-3 w-3" />}
                    </Button>
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => toggleBookmark(c)}>
                      {bookmarks.some((b) => b.id === c.id) ? (
                        <BookmarkCheck className="h-4 w-4 text-amber-500" />
                      ) : (
                        <Bookmark className="h-4 w-4" />
                      )}
                    </Button>
                    <a
                      href={`https://scholar.google.com/scholar?q=${encodeURIComponent(c.title)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-amber-600 underline"
                    >
                      Scholar
                    </a>
                    <Button variant="ghost" size="sm" className="h-6 px-2" onClick={() => toggleCompare(c.id)}>
                      <X className="h-4 w-4" />
                    </Button>
                  </td>
                ))}
              </tr>
            </tbody>
          </table>
          {comparisonSummary && (
            <div className="mt-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
              <button
                onClick={() => setComparisonSummary("")}
                className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                aria-label="Close comparison summary"
              >
                <X className="h-3 w-3" />
              </button>
              <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                <Sparkles className="h-4 w-4" /> Comparison Summary
              </p>
              <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                {parseBullets(comparisonSummary).map((b, i) => (
                  <li key={i}>{b}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      <div className={gridColumns}>
        {searches.map((search, sidx) => search.results.length > 0 && (
          <div key={search.id} className="space-y-8 border p-6 rounded-lg">
            <div className="font-bold text-lg mb-4 truncate" title={search.query}>
              {search.query}
            </div>
            
            {/* Charts */}
            <div
              className={
                search.results.length === 1
                  ? "md:grid md:grid-cols-3 gap-8"
                  : ""
              }
            >
              {/* Year distribution chart for this search */}
              <div className="h-64 w-full mb-8 md:mb-0">
                <h3 className="text-sm font-medium mb-2">Publication Years</h3>
                <ResponsiveContainer width="100%" height="85%">
                  {viewType === "bar" ? (
                    <BarChart
                      data={getYearData(search.results)}
                      margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                    >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis allowDecimals={false} />
                    <Bar
                      dataKey="count"
                      fill="#f59e0b"
                      onMouseEnter={(data, _idx, e) =>
                        showHoverTooltip(
                          data.payload.year,
                          getArticlesByYear(search.results, data.payload.year),
                          e
                        )
                      }
                      onMouseLeave={scheduleHideTooltip}
                      onClick={(data) => {
                        if (data && data.payload?.year) {
                          window.open(
                            `https://pubmed.ncbi.nlm.nih.gov/?term=${data.payload.year}[dp]`,
                            "_blank",
                          );
                        }
                      }}
                    />
                  </BarChart>
                ) : (
                  <LineChart
                    data={getYearData(search.results)}
                    margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="year" />
                    <YAxis allowDecimals={false} />
                    <Line
                      type="monotone"
                      dataKey="count"
                      stroke="#f59e0b"
                      activeDot={{
                        onMouseEnter: (e: any, payload: any) =>
                          showHoverTooltip(
                            payload.payload.year,
                            getArticlesByYear(search.results, payload.payload.year),
                            e
                          ),
                        onMouseLeave: scheduleHideTooltip,
                        onClick: (_e: any, payload: any) => {
                          if (payload?.payload?.year) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${payload.payload.year}[dp]`,
                              "_blank",
                            );
                          }
                        },
                      }}
                    />
                  </LineChart>
                )}
                </ResponsiveContainer>
              </div>

              {/* Journal chart for this search */}
              {getJournalData(search.results).length > 0 && (
                <div className="mb-8 md:mb-0">
                  <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Journals</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setJournalCounts((c) =>
                          c.map((v, i) => (i === sidx ? Math.min(v + 5, getJournalData(search.results).length) : v)),
                        )
                      }
                      disabled={journalCounts[sidx] >= getJournalData(search.results).length}
                    >
                      Show 5 More
                    </Button>
                    {journalCounts[sidx] > 5 && (
                      <Button size="sm" variant="outline" onClick={() =>
                        setJournalCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                      }>
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ height: `${Math.max(200, (journalCounts[sidx] || 5) * 40)}px` }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getJournalData(search.results).slice(0, journalCounts[sidx] || 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 140, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="journal" 
                        type="category" 
                        width={140}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{payload.value}</title>
                              <text 
                                x={0} 
                                y={0} 
                                dy={4} 
                                textAnchor="end" 
                                fontSize={12}
                                fontWeight="500"
                                fill="#444"
                              >
                                {payload.value.length > 22 ? `${payload.value.substring(0, 20)}...` : payload.value}
                              </text>
                            </g>
                          );
                        }}
                        interval={0}
                      />
                      <Bar
                        dataKey="value"
                        fill="#3b82f6"
                        onMouseEnter={(data, _idx, e) =>
                          showHoverTooltip(
                            data.payload.journal,
                            getArticlesByJournal(
                              search.results,
                              data.payload.journal,
                            ),
                            e,
                          )
                        }
                        onMouseLeave={scheduleHideTooltip}
                        onClick={(data) => {
                          if (data && data.payload?.journal) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.journal)}[jour]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Author chart for this search */}
              {getAuthorData(search.results).length > 0 && (
                <div className="mb-8 md:mb-0">
                  <div className="flex justify-between items-center mb-2">
                  <h3 className="text-sm font-medium">Top Authors</h3>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() =>
                        setAuthorCounts((c) =>
                          c.map((v, i) => (i === sidx ? Math.min(v + 5, getAuthorData(search.results).length) : v)),
                        )
                      }
                      disabled={authorCounts[sidx] >= getAuthorData(search.results).length}
                    >
                      Show 5 More
                    </Button>
                    {authorCounts[sidx] > 5 && (
                      <Button size="sm" variant="outline" onClick={() =>
                        setAuthorCounts((c) => c.map((v, i) => (i === sidx ? 5 : v)))
                      }>
                        Top 5
                      </Button>
                    )}
                  </div>
                </div>
                <div style={{ height: `${Math.max(200, (authorCounts[sidx] || 5) * 40)}px` }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={getAuthorData(search.results).slice(0, authorCounts[sidx] || 5)}
                      layout="vertical"
                      margin={{ top: 5, right: 30, left: 140, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis type="number" allowDecimals={false} />
                      <YAxis 
                        dataKey="author" 
                        type="category" 
                        width={140}
                        tick={(props) => {
                          const { x, y, payload } = props;
                          return (
                            <g transform={`translate(${x},${y})`}>
                              <title>{payload.value}</title>
                              <text 
                                x={0} 
                                y={0} 
                                dy={4} 
                                textAnchor="end" 
                                fontSize={12}
                                fontWeight="500"
                                fill="#444"
                              >
                                {payload.value.length > 22 ? `${payload.value.substring(0, 20)}...` : payload.value}
                              </text>
                            </g>
                          );
                        }}
                        interval={0}
                      />
                      <Bar
                        dataKey="value"
                        fill="#10b981"
                        onMouseEnter={(data, _idx, e) =>
                          showHoverTooltip(
                            data.payload.author,
                            getArticlesByAuthor(
                              search.results,
                              data.payload.author,
                            ),
                            e,
                          )
                        }
                        onMouseLeave={scheduleHideTooltip}
                        onClick={(data) => {
                          if (data && data.payload?.author) {
                            window.open(
                              `https://pubmed.ncbi.nlm.nih.gov/?term=${encodeURIComponent(data.payload.author)}[auth]`,
                              "_blank",
                            );
                          }
                        }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                  </div>
                </div>
              )}
            </div>
            
            {/* Export and AI actions */}
            <div className="flex flex-wrap justify-end mb-6 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => exportSearchCsv(sidx)}
                className="gap-1"
              >
                <Download className="h-4 w-4" /> Export Results
              </Button>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => summarizeSearchResults(sidx)}
                  disabled={loading[`search-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`search-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Summarizing...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Summarize Search
                    </>
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => suggestQueries(sidx)}
                  disabled={loading[`query-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`query-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Query Ideas
                    </>
                  )}
                </Button>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => suggestResearchQuestions(sidx)}
                  disabled={loading[`research-${sidx}`]}
                  className="gap-1"
                >
                  {loading[`research-${sidx}`] ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" /> Thinking...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4" /> Research Questions
                    </>
                  )}
                </Button>
              </div>
            </div>
            {searchSummaries[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-blue-50 dark:bg-blue-900/40 relative">
                <button 
                  onClick={() => setSearchSummaries(prev => ({...prev, [sidx]: ""}))}
                  className="absolute top-1 right-1 text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800 rounded-full p-1"
                  aria-label="Close summary"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 mb-1">
                  <Sparkles className="h-4 w-4" /> Search Summary
                </p>
                <ul className="list-[circle] pl-4 text-sm text-blue-700 dark:text-blue-300">
                  {parseBullets(searchSummaries[sidx]).map((b, i) => (
                    <li key={i}>{b}</li>
                  ))}
                </ul>
              </div>
            )}
            {querySuggestions[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-amber-50 dark:bg-amber-900/40 relative">
                <button 
                  onClick={() => setQuerySuggestions(prev => ({...prev, [sidx]: []}))}
                  className="absolute top-1 right-1 text-amber-700 dark:text-amber-300 hover:bg-amber-100 dark:hover:bg-amber-800 rounded-full p-1"
                  aria-label="Close query suggestions"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-amber-700 dark:text-amber-300 flex items-center gap-1 mb-1">
                  <Lightbulb className="h-4 w-4" /> Query Suggestions
                </p>
                <ul className="list-[circle] pl-4 text-sm text-amber-700 dark:text-amber-300">
                  {querySuggestions[sidx].map((q, i) => (
                    <li key={i} className="cursor-pointer hover:underline" onClick={() => {
                      setSearches(prev => prev.map((s, idx) => 
                        idx === sidx ? {...s, query: q.replace(/^[•\s-]+/, '')} : s
                      ));
                    }}>
                      {q}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            {researchQuestions[sidx] && (
              <div className="mb-4 p-2 rounded-md bg-green-50 dark:bg-green-900/40 relative">
                <button 
                  onClick={() => setResearchQuestions(prev => ({...prev, [sidx]: []}))}
                  className="absolute top-1 right-1 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-800 rounded-full p-1"
                  aria-label="Close research questions"
                >
                  <X className="h-3 w-3" />
                </button>
                <p className="font-semibold text-green-700 dark:text-green-300 flex items-center gap-1 mb-1">
                  <Lightbulb className="h-4 w-4" /> Research Questions
                </p>
                <ul className="list-[circle] pl-4 text-sm text-green-700 dark:text-green-300">
                  {researchQuestions[sidx].map((q, i) => (
                    <li key={i}>{q}</li>
                  ))}
                </ul>
              </div>
            )}
            
            {/* Results for this search */}
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium">Search Results ({search.results.length})</h3>
                {search.results.length < search.totalCount && search.results.length > 0 && (
                  <Button
                    onClick={() => loadMore(sidx)}
                    className="bg-amber-400 text-black hover:bg-amber-500"
                    disabled={search.isSearching}
                  >
                    Load More
                  </Button>
                )}
              </div>
              {search.results.map((item, idx) => (
                <Card key={`${sidx}-${idx}`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <CardTitle className="text-lg flex-1 pr-2">
                        {highlightText(item.title, search.query)}
                      </CardTitle>
                      <div className="flex items-center gap-2">
                        <Checkbox
                          checked={compareIds.includes(item.id)}
                          onCheckedChange={() => toggleCompare(item.id)}
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBookmark(item)}
                          className="h-6 px-2 hover:bg-transparent"
                        >
                          {bookmarks.some((b) => b.id === item.id) ? (
                            <BookmarkCheck className="h-4 w-4 text-amber-500" />
                          ) : (
                            <Bookmark className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {(item.journal || item.pubdate) && (
                      <p className="text-sm text-muted-foreground mb-1">
                        {item.journal} {item.pubdate ? `• ${item.pubdate}` : ""}
                      </p>
                    )}
                    {item.authors && (
                      <p className="text-sm text-muted-foreground mb-2">
                        {item.authors}
                      </p>
                    )}
                    {item.abstract && (
                      <div className="text-sm mb-2">
                        {highlightText(
                          expandedIds.includes(`${sidx}-${idx}`)
                            ? item.abstract
                            : `${item.abstract.substring(0, 250)}...`,
                          search.query,
                        )}
                        <Button
                          variant="link"
                          size="sm"
                          className="h-4 px-1"
                          onClick={() => toggleAbstract(`${sidx}-${idx}`)}
                        >
                          {expandedIds.includes(`${sidx}-${idx}`) ? "Show Less" : "Show More"}
                        </Button>
                      </div>
                    )}
                    {(item.publicationTypes?.length || item.meshTerms?.length) && (
                      <div className="text-xs text-muted-foreground mb-2 space-y-1">
                        {item.publicationTypes && item.publicationTypes.length > 0 && (
                          <p>
                            <strong>Publication types:</strong> {item.publicationTypes.join('; ')}
                          </p>
                        )}
                        {item.meshTerms && item.meshTerms.length > 0 && (
                          <p>
                            <strong>Keywords:</strong> {item.meshTerms.join('; ')}
                          </p>
                        )}
                      </div>
                    )}
                    {item.abstract && (
                      <div className="text-xs flex items-center gap-2 mb-2">
                        <div className="flex items-center gap-1">
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => summarizeArticle(item)}
                            disabled={loading[`article-${item.id}`]}
                            className="gap-1"
                          >
                            {loading[`article-${item.id}`] ? (
                              <>
                                <Loader2 className="h-3 w-3 animate-spin" /> Working...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-3 w-3" /> Summarize
                              </>
                            )}
                          </Button>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => plainLanguageSummary(item)}
                            disabled={loading[`plain-${item.id}`]}
                            className="gap-1"
                          >
                            {loading[`plain-${item.id}`] ? (
                              <>
                                <Loader2 className="h-3 w-3 animate-spin" /> Simplifying...
                              </>
                            ) : (
                              <>
                                <Sparkles className="h-3 w-3" /> Plain Summary
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                    {articleSummaries[item.id] && (
                      <div className="mb-2 p-2 rounded bg-blue-50 dark:bg-blue-900/40">
                        <p className="font-semibold text-blue-700 dark:text-blue-300 flex items-center gap-1 text-xs mb-1">
                          <Sparkles className="h-3 w-3" /> Article Summary
                        </p>
                        <ul className="list-[circle] pl-4 text-xs text-blue-700 dark:text-blue-300">
                          {parseBullets(articleSummaries[item.id]).map((b, i) => (
                            <li key={i}>{b}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {plainSummaries[item.id] && (
                      <div className="mb-2 p-2 rounded bg-orange-50 dark:bg-orange-900/40">
                        <p className="font-semibold text-orange-700 dark:text-orange-300 flex items-center gap-1 text-xs mb-1">
                          <Lightbulb className="h-3 w-3" /> Plain Summary
                        </p>
                        <ul className="list-[circle] pl-4 text-xs text-orange-700 dark:text-orange-300">
                          {parseBullets(plainSummaries[item.id]).map((b, i) => (
                            <li key={i}>{b}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {item.abstract && extractNctIds(item.abstract).length > 0 && (
                      <div className="text-xs mb-2">
                        <span className="font-medium">Clinical Trials:</span>{" "}
                        {extractNctIds(item.abstract).map((id, i) => (
                          <React.Fragment key={id}>
                            {i > 0 && ", "}
                            <a
                              href={`https://clinicaltrials.gov/study/${id}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                            >
                              {id}
                            </a>
                          </React.Fragment>
                        ))}
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <a
                        href={`https://pubmed.ncbi.nlm.nih.gov/${item.id}/`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                      >
                        View on PubMed
                      </a>
                      <a
                        href={`https://scholar.google.com/scholar?q=${encodeURIComponent(item.title)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-amber-600 hover:text-amber-800 dark:text-amber-400 dark:hover:text-amber-300"
                      >
                        Google Scholar
                      </a>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 px-2 hover:bg-transparent"
                        onClick={() => copyCitation(item, `${sidx}-${idx}`)}
                      >
                        {copiedId === `${sidx}-${idx}` ? (
                          <span className="text-xs text-green-600">Copied</span>
                        ) : (
                          <span className="text-xs inline-flex items-center gap-1">
                            <Clipboard className="h-3 w-3" /> Copy Citation
                          </span>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              {search.results.length < search.totalCount && search.results.length > 0 && (
                <div className="mt-4 text-center">
                  <Button
                    onClick={() => loadMore(sidx)}
                    className="bg-amber-400 text-black hover:bg-amber-500"
                    disabled={search.isSearching}
                  >
                    Load More
                  </Button>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      {hoverTooltip && (
        <div
          className="fixed z-50 bg-white dark:bg-gray-800 border rounded p-2 text-xs max-w-xs"
          style={{ left: hoverTooltip.x + 10, top: hoverTooltip.y + 10 }}
          onMouseEnter={cancelHideTooltip}
          onMouseLeave={() => setHoverTooltip(null)}
        >
          <p className="font-semibold mb-1">
            {hoverTooltip.label}: {hoverTooltip.articles.length}
          </p>
          <ul className="max-h-40 overflow-y-auto list-disc pl-4 space-y-1">
            {hoverTooltip.articles.map((a) => (
              <li key={a.id}>
                <a
                  href={`https://pubmed.ncbi.nlm.nih.gov/${a.id}/`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-amber-600 underline"
                >
                  {a.title}
                </a>
              </li>
            ))}
          </ul>
        </div>
      )}
      {compareItems.length > 1 && (
        <Button
          onClick={scrollToComparison}
          className="fixed bottom-4 right-4 z-50 rounded-full bg-amber-400 text-black hover:bg-amber-500 shadow-md"
          size="sm"
        >
          View Comparison
        </Button>
      )}
    </div>
  );
}

// Main component with Suspense boundary
export default function PubMedExplorerPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PubMedExplorerContent />
    </Suspense>
  );
}
